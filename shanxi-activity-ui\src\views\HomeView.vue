<template>
  <div class="home-view">
    <el-card>
      <template #header>
        <h2>欢迎使用陕西平台活动工具</h2>
      </template>

      <div class="welcome-content">
        <el-row :gutter="24">
          <el-col :span="8" v-if="canManageActivity">
            <el-card class="feature-card" shadow="hover">
              <el-icon class="feature-icon">
                <Trophy />
              </el-icon>
              <h3>活动管理</h3>
              <p>创建和管理盲盒活动，设置奖励和任务</p>
              <el-button type="primary" @click="$router.push('/activity/create')">
                创建活动
              </el-button>
            </el-card>
          </el-col>

          <el-col :span="8" v-if="isPlayer">
            <el-card class="feature-card" shadow="hover">
              <el-icon class="feature-icon">
                <Monitor />
              </el-icon>
              <h3>参与活动</h3>
              <p>查看可参与的活动，完成任务获得抽奖机会</p>
              <el-button type="primary" @click="$router.push('/player/activities')">
                我的活动
              </el-button>
            </el-card>
          </el-col>

          <el-col :span="8">
            <el-card class="feature-card" shadow="hover">
              <el-icon class="feature-icon">
                <DataAnalysis />
              </el-icon>
              <h3>数据统计</h3>
              <p>查看活动数据和中奖记录</p>
              <el-button type="primary" @click="$router.push('/statistics')">
                查看数据
              </el-button>
            </el-card>
          </el-col>
        </el-row>

        <div class="user-info-section" v-if="userInfo">
          <h3>用户信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="昵称">{{ userInfo.nickname }}</el-descriptions-item>
            <el-descriptions-item label="角色">{{ getRoleText(userInfo.role) }}</el-descriptions-item>
            <el-descriptions-item label="通宝余额">{{ formatTongbao(userInfo.tongbaoBalance) }}</el-descriptions-item>
            <el-descriptions-item label="用户ID">{{ userInfo.gameUserId }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Trophy, Monitor, DataAnalysis } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores'
import { formatTongbao } from '@/utils'

const userStore = useUserStore()

const userInfo = computed(() => userStore.userInfo)
const canManageActivity = computed(() => userStore.canManageActivity)
const isPlayer = computed(() => userStore.isPlayer)

const getRoleText = (role: string) => {
  const roleMap: Record<string, string> = {
    alliance_leader: '盟主',
    partner: '合伙人',
    player: '玩家'
  }
  return roleMap[role] || role
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.home-view {
  .welcome-content {
    .feature-card {
      text-align: center;
      margin-bottom: $spacing-lg;

      .feature-icon {
        font-size: 48px;
        color: $primary-color;
        margin-bottom: $spacing-md;
      }

      h3 {
        margin: $spacing-md 0;
        color: $text-color-primary;
      }

      p {
        color: $text-color-secondary;
        margin-bottom: $spacing-lg;
      }
    }

    .user-info-section {
      margin-top: $spacing-xl;

      h3 {
        margin-bottom: $spacing-md;
        color: $text-color-primary;
      }
    }
  }
}
</style>
