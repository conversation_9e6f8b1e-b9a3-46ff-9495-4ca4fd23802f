<template>
  <el-dialog
    v-model="visible"
    title="奖品列表"
    width="600px"
    class="rewards-list-dialog"
  >
    <div class="rewards-content">
      <div class="rewards-grid">
        <div 
          v-for="reward in rewards" 
          :key="reward.id"
          class="reward-item"
          :class="{ 'is-rare': reward.probability && reward.probability < 5 }"
        >
          <div class="reward-card">
            <div class="reward-header">
              <div class="reward-type">
                <StatusTag 
                  :status="reward.rewardType === 'tongbao' ? 'success' : 'warning'"
                  size="small"
                >
                  {{ reward.rewardType === 'tongbao' ? '通宝' : '实物' }}
                </StatusTag>
              </div>
              <div class="reward-probability">
                {{ reward.probability?.toFixed(2) }}%
              </div>
            </div>
            
            <div class="reward-content">
              <div class="reward-icon">
                <el-icon size="32">
                  <component :is="getRewardIcon(reward)" />
                </el-icon>
              </div>
              
              <div class="reward-info">
                <h4 class="reward-name">{{ reward.rewardName }}</h4>
                
                <div class="reward-value" v-if="reward.rewardType === 'tongbao'">
                  {{ formatTongbao(reward.tongbaoAmount) }}
                </div>
                
                <div class="reward-description" v-if="reward.physicalItem">
                  {{ reward.physicalItem }}
                </div>
              </div>
            </div>
            
            <div class="reward-footer">
              <div class="quantity-info">
                <span class="label">每日数量：</span>
                <span class="value">{{ reward.dailyQuantity }}</span>
              </div>
              
              <div class="remaining-info" v-if="reward.remainingQuantity !== undefined">
                <span class="label">剩余：</span>
                <span 
                  class="value"
                  :class="{ 'low-quantity': reward.remainingQuantity < 10 }"
                >
                  {{ reward.remainingQuantity }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 概率说明 -->
      <div class="probability-note">
        <el-alert
          title="概率说明"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <ul class="note-list">
              <li>以上概率为理论概率，实际中奖情况可能存在差异</li>
              <li>每日奖品数量有限，先到先得</li>
              <li>概率低于5%的奖品为稀有奖品</li>
            </ul>
          </template>
        </el-alert>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Coin, Gift, Trophy } from '@element-plus/icons-vue'
import { StatusTag } from '@/components/common'
import { formatTongbao } from '@/utils'
import type { Reward } from '@/types'

interface Props {
  modelValue: boolean
  rewards: Reward[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法
const getRewardIcon = (reward: Reward) => {
  if (reward.rewardType === 'tongbao') {
    return Coin
  } else if (reward.probability && reward.probability < 5) {
    return Trophy // 稀有奖品用奖杯图标
  } else {
    return Gift
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.rewards-list-dialog {
  :deep(.el-dialog) {
    border-radius: $border-radius-lg;
  }
  
  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
    color: white;
    
    .el-dialog__title {
      color: white;
      font-weight: 600;
    }
  }
  
  .rewards-content {
    .rewards-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: $spacing-md;
      margin-bottom: $spacing-xl;
      
      .reward-item {
        &.is-rare {
          .reward-card {
            border: 2px solid $warning-color;
            background: linear-gradient(135deg, lighten($warning-color, 45%) 0%, lighten($warning-color, 40%) 100%);
            
            .reward-header {
              .reward-probability {
                color: $warning-color;
                font-weight: 700;
              }
            }
          }
        }
        
        .reward-card {
          border: 1px solid $border-color-light;
          border-radius: $border-radius-base;
          padding: $spacing-md;
          background: $background-color-white;
          transition: all 0.3s ease;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
          
          .reward-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: $spacing-md;
            
            .reward-probability {
              font-size: $font-size-sm;
              font-weight: 600;
              color: $primary-color;
            }
          }
          
          .reward-content {
            display: flex;
            align-items: center;
            gap: $spacing-md;
            margin-bottom: $spacing-md;
            
            .reward-icon {
              flex-shrink: 0;
              color: $primary-color;
            }
            
            .reward-info {
              flex: 1;
              
              .reward-name {
                margin: 0 0 $spacing-xs 0;
                font-size: $font-size-base;
                color: $text-color-primary;
              }
              
              .reward-value {
                font-size: $font-size-lg;
                font-weight: 600;
                color: $success-color;
                margin-bottom: $spacing-xs;
              }
              
              .reward-description {
                font-size: $font-size-sm;
                color: $text-color-secondary;
              }
            }
          }
          
          .reward-footer {
            display: flex;
            justify-content: space-between;
            font-size: $font-size-sm;
            
            .quantity-info,
            .remaining-info {
              .label {
                color: $text-color-secondary;
              }
              
              .value {
                color: $text-color-primary;
                font-weight: 500;
                
                &.low-quantity {
                  color: $error-color;
                  font-weight: 600;
                }
              }
            }
          }
        }
      }
    }
    
    .probability-note {
      .note-list {
        margin: 0;
        padding-left: $spacing-lg;
        
        li {
          margin-bottom: $spacing-xs;
          color: $text-color-secondary;
          font-size: $font-size-sm;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
  
  .dialog-footer {
    text-align: center;
  }
}

// 移动端适配
@media (max-width: $screen-md) {
  .rewards-list-dialog {
    :deep(.el-dialog) {
      width: 90% !important;
      margin: 5vh auto;
    }
    
    .rewards-grid {
      grid-template-columns: 1fr !important;
    }
  }
}
</style>
