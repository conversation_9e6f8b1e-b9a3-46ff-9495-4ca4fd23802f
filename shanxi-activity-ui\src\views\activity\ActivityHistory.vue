<template>
  <div class="activity-history">
    <BaseCard title="历史活动">
      <template #extra>
        <el-button @click="$router.push('/activity/list')">
          <el-icon><List /></el-icon>
          当前活动
        </el-button>
      </template>

      <!-- 筛选和统计 -->
      <div class="filter-section">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-input
              v-model="searchParams.search"
              placeholder="搜索活动名称"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>

          <el-col :span="4">
            <el-select
              v-model="searchParams.status"
              placeholder="活动状态"
              clearable
              @change="handleFilter"
            >
              <el-option label="全部状态" value="" />
              <el-option label="已结束" value="ended" />
              <el-option label="已关闭" value="closed" />
            </el-select>
          </el-col>

          <el-col :span="4">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
            />
          </el-col>

          <el-col :span="4">
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </el-col>
        </el-row>

        <!-- 统计信息 -->
        <div class="statistics-section">
          <el-row :gutter="24">
            <el-col :span="6">
              <el-statistic
                title="历史活动总数"
                :value="total"
                :value-style="{ color: '#1890ff' }"
              />
            </el-col>
            <el-col :span="6">
              <el-statistic
                title="总通宝发放"
                :value="totalTongbaoDistributed"
                :formatter="formatTongbao"
                :value-style="{ color: '#52c41a' }"
              />
            </el-col>
            <el-col :span="6">
              <el-statistic
                title="总参与人次"
                :value="totalParticipants"
                :value-style="{ color: '#fa8c16' }"
              />
            </el-col>
            <el-col :span="6">
              <el-statistic
                title="平均活动时长"
                :value="averageDuration"
                suffix="天"
                :value-style="{ color: '#722ed1' }"
              />
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 历史活动列表 -->
      <div class="history-container">
        <LoadingSpinner v-if="loading" text="加载历史活动..." />

        <div v-else-if="activities.length === 0" class="empty-container">
          <EmptyState
            type="no-data"
            title="暂无历史活动"
            description="还没有已结束或已关闭的活动"
          />
        </div>

        <div v-else class="history-table">
          <el-table :data="activities" border stripe>
            <el-table-column
              prop="activityName"
              label="活动名称"
              min-width="150"
            >
              <template #default="{ row }">
                <el-link
                  type="primary"
                  @click="handleViewDetail(row)"
                  :underline="false"
                >
                  {{ row.activityName }}
                </el-link>
              </template>
            </el-table-column>

            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <StatusTag :status="row.status" />
              </template>
            </el-table-column>

            <el-table-column prop="totalTongbao" label="总通宝" width="120">
              <template #default="{ row }">
                {{ formatTongbao(row.totalTongbao) }}
              </template>
            </el-table-column>

            <el-table-column
              prop="remainingTongbao"
              label="剩余通宝"
              width="120"
            >
              <template #default="{ row }">
                {{ formatTongbao(row.remainingTongbao) }}
              </template>
            </el-table-column>

            <el-table-column
              prop="participantsCount"
              label="参与人数"
              width="100"
            />

            <el-table-column
              prop="creatorNickname"
              label="创建者"
              width="120"
            />

            <el-table-column prop="startTime" label="开始时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.startTime) }}
              </template>
            </el-table-column>

            <el-table-column prop="endTime" label="结束时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.endTime) }}
              </template>
            </el-table-column>

            <el-table-column label="活动时长" width="100">
              <template #default="{ row }">
                {{ getActivityDuration(row) }}天
              </template>
            </el-table-column>

            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <div class="action-buttons">
                  <el-button size="small" @click="handleViewDetail(row)">
                    查看详情
                  </el-button>

                  <el-button
                    size="small"
                    type="primary"
                    @click="handleViewStatistics(row)"
                  >
                    数据统计
                  </el-button>

                  <el-button
                    v-if="canExportData"
                    size="small"
                    type="success"
                    @click="handleExportData(row)"
                  >
                    导出数据
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          v-model:current-page="searchParams.page"
          v-model:page-size="searchParams.limit"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </BaseCard>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { List, Search, Refresh } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import {
  BaseCard,
  StatusTag,
  LoadingSpinner,
  EmptyState,
} from "@/components/common";
import { useActivityStore, useUserStore } from "@/stores";
import { formatTongbao, formatDateTime, debounce } from "@/utils";
import type { Activity, ActivityListParams } from "@/types";

const router = useRouter();
const activityStore = useActivityStore();
const userStore = useUserStore();

// 响应式数据
const loading = ref(false);
const dateRange = ref<[string, string] | null>(null);
const searchParams = reactive<ActivityListParams>({
  page: 1,
  limit: 20,
  status: "ended", // 默认显示已结束的活动
  scope: "my",
  search: "",
});

// 计算属性
const activities = computed(() => activityStore.activities);
const total = computed(() => activityStore.total);
const canExportData = computed(() => userStore.isAllianceLeader);

// 统计数据
const totalTongbaoDistributed = computed(() => {
  return activities.value.reduce((total, activity) => {
    return total + (activity.totalTongbao - activity.remainingTongbao);
  }, 0);
});

const totalParticipants = computed(() => {
  return activities.value.reduce((total, activity) => {
    return total + activity.participantsCount;
  }, 0);
});

const averageDuration = computed(() => {
  if (activities.value.length === 0) return 0;
  const totalDays = activities.value.reduce((total, activity) => {
    return total + getActivityDuration(activity);
  }, 0);
  return Math.round(totalDays / activities.value.length);
});

// 方法
const getActivityDuration = (activity: Activity) => {
  const start = new Date(activity.startTime);
  const end = new Date(activity.endTime);
  return Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
};

const loadHistoryActivities = async () => {
  loading.value = true;
  try {
    // 只加载已结束和已关闭的活动
    const params = {
      ...searchParams,
      status: searchParams.status || "ended,closed",
    };
    await activityStore.fetchActivities(params);
  } catch (error) {
    console.error("Failed to load history activities:", error);
    ElMessage.error("加载历史活动失败");
  } finally {
    loading.value = false;
  }
};

// 搜索处理（防抖）
const handleSearch = debounce(() => {
  searchParams.page = 1;
  loadHistoryActivities();
}, 500);

// 筛选处理
const handleFilter = () => {
  searchParams.page = 1;
  loadHistoryActivities();
};

// 日期范围变化
const handleDateRangeChange = (dates: [string, string] | null) => {
  // 这里可以添加日期筛选逻辑
  searchParams.page = 1;
  loadHistoryActivities();
};

// 刷新
const handleRefresh = () => {
  loadHistoryActivities();
};

// 分页处理
const handlePageChange = (page: number) => {
  searchParams.page = page;
  loadHistoryActivities();
};

const handlePageSizeChange = (size: number) => {
  searchParams.limit = size;
  searchParams.page = 1;
  loadHistoryActivities();
};

// 操作处理
const handleViewDetail = (activity: Activity) => {
  router.push(`/activity/${activity.id}`);
};

const handleViewStatistics = (activity: Activity) => {
  router.push(`/activity/${activity.id}/statistics`);
};

const handleExportData = (activity: Activity) => {
  ElMessage.info("数据导出功能开发中");
};

// 生命周期
onMounted(() => {
  loadHistoryActivities();
});
</script>

<style lang="scss" scoped>
.activity-history {
  .filter-section {
    margin-bottom: $spacing-lg;
    padding-bottom: $spacing-lg;
    border-bottom: 1px solid $border-color-light;

    .statistics-section {
      margin-top: $spacing-lg;
      padding-top: $spacing-lg;
      border-top: 1px solid $border-color-light;
    }
  }

  .history-container {
    min-height: 400px;

    .empty-container {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 400px;
    }

    .history-table {
      .action-buttons {
        display: flex;
        gap: $spacing-xs;
        flex-wrap: wrap;
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: $spacing-xl;
  }
}

// 移动端适配
@media (max-width: $screen-md) {
  .activity-history {
    .filter-section {
      .el-col {
        margin-bottom: $spacing-sm;
      }

      .statistics-section {
        .el-col {
          margin-bottom: $spacing-md;
        }
      }
    }

    .history-table {
      .action-buttons {
        flex-direction: column;

        .el-button {
          width: 100%;
        }
      }
    }
  }
}
</style>
