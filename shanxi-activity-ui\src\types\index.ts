// 用户角色类型
export type UserRole = 'alliance_leader' | 'partner' | 'player'

// 活动状态
export type ActivityStatus = 'not_started' | 'running' | 'ended' | 'closed'

// 活动类型
export type ActivityType = 'blind_box'

// 奖励类型
export type RewardType = 'tongbao' | 'physical'

// 任务类型
export type TaskType = 'login' | 'game_rounds' | 'contribution'

// 任务刷新类型
export type RefreshType = 'daily' | 'weekly' | 'never'

// 用户信息
export interface UserInfo {
  gameUserId: string
  nickname: string
  role: UserRole
  tongbaoBalance: number
  avatar?: string
}

// 活动基础信息
export interface Activity {
  id: number
  activityName: string
  activityType: ActivityType
  totalTongbao: number
  remainingTongbao: number
  startTime: string
  endTime: string
  status: ActivityStatus
  participantsCount: number
  creatorNickname: string
}

// 奖励配置
export interface Reward {
  id?: number
  rewardName: string
  rewardType: RewardType
  tongbaoAmount: number
  physicalItem?: string
  weight: number
  dailyQuantity: number
  probability?: number
  remainingQuantity?: number
}

// 任务配置
export interface Task {
  id?: number
  taskType: TaskType
  taskName: string
  targetValue: number
  rewardChances: number
  refreshType: RefreshType
  currentProgress?: number
  completed?: boolean
  refreshCountdown?: number
}

// 活动详情
export interface ActivityDetail extends Activity {
  rewards: Reward[]
  tasks: Task[]
}

// 创建活动请求
export interface CreateActivityRequest {
  activityName: string
  activityType: ActivityType
  totalTongbao: number
  startTime: string
  endTime: string
  rewards: Omit<Reward, 'id' | 'probability' | 'remainingQuantity'>[]
  tasks: Omit<Task, 'id' | 'currentProgress' | 'completed' | 'refreshCountdown'>[]
}

// 抽奖结果
export interface DrawResult {
  drawId: number
  reward: Reward
  remainingChances: number
}

// 抽奖次数信息
export interface DrawChances {
  totalChances: number
  usedChances: number
  remainingChances: number
}

// 抽奖记录
export interface DrawRecord {
  id: number
  playerNickname: string
  rewardName: string
  drawTime: string
  isMyRecord?: boolean
}

// 跑马灯消息
export interface MarqueeMessage {
  id: number
  playerNickname: string
  rewardName: string
  messageContent: string
  createdAt: string
}

// 活动统计数据
export interface ActivityStatistics {
  participantsCount: number
  totalDraws: number
  totalRewardsDistributed: number
  playerStatistics: PlayerStatistic[]
  physicalRewards: PhysicalRewardStat[]
}

// 玩家统计
export interface PlayerStatistic {
  playerId: string
  playerNickname: string
  totalDraws: number
  totalTongbaoWon: number
  physicalRewards: PhysicalRewardStat[]
}

// 实物奖励统计
export interface PhysicalRewardStat {
  rewardName: string
  quantity: number
}

// API响应基础结构
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp?: string
}

// 分页请求参数
export interface PaginationParams {
  page: number
  limit: number
}

// 分页响应数据
export interface PaginationResponse<T> {
  items: T[]
  total: number
  page: number
  limit: number
}

// 活动列表查询参数
export interface ActivityListParams extends PaginationParams {
  status?: ActivityStatus
  scope?: 'my' | 'all'
  search?: string
}

// 抽奖记录查询参数
export interface DrawRecordParams {
  activityId: number
  type?: 'all' | 'my'
  limit?: number
}
