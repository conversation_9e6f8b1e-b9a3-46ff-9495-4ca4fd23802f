import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 简化的路由配置 - 只保留三个核心页面
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'BasicConfig',
    component: () => import('@/views/activity/CreateActivity.vue'),
    meta: { title: '活动基础配置' }
  },
  {
    path: '/detail-config',
    name: 'DetailConfig',
    component: () => import('@/views/activity/DetailConfig.vue'),
    meta: { title: '活动详细配置' }
  },
  {
    path: '/participate/:id?',
    name: 'Participate',
    component: () => import('@/views/player/ActivityParticipation.vue'),
    meta: { title: '活动参与' }
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: { title: '页面不存在' }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

export default router
