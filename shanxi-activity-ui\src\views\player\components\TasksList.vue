<template>
  <div class="tasks-list">
    <div class="tasks-header">
      <h3>任务列表</h3>
      <div class="tasks-summary">
        <el-statistic 
          title="已完成" 
          :value="completedCount"
          :value-style="{ color: '#52c41a' }"
        />
        <el-statistic 
          title="总任务" 
          :value="totalCount"
          :value-style="{ color: '#1890ff' }"
        />
        <el-statistic 
          title="完成率" 
          :value="completionRate"
          suffix="%"
          :value-style="{ color: '#722ed1' }"
        />
      </div>
    </div>
    
    <div class="tasks-container">
      <LoadingSpinner v-if="loading" text="加载任务..." />
      
      <div v-else-if="tasks.length === 0" class="empty-container">
        <EmptyState 
          type="no-data" 
          title="暂无任务"
          description="当前活动没有设置任务"
        />
      </div>
      
      <div v-else class="tasks-grid">
        <div 
          v-for="task in sortedTasks" 
          :key="task.id"
          class="task-item"
        >
          <TaskCard 
            :task="task"
            @complete="handleTaskComplete"
            @refresh="handleTaskRefresh"
          />
        </div>
      </div>
    </div>
    
    <!-- 任务刷新提示 -->
    <div class="refresh-notice" v-if="nextRefreshTime">
      <el-alert
        :title="`任务将在 ${nextRefreshTime} 刷新`"
        type="info"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { LoadingSpinner, EmptyState } from '@/components/common'
import TaskCard from './TaskCard.vue'
import { formatCountdown } from '@/utils'
import type { Task } from '@/types'

interface Props {
  activityId: number
  tasks: Task[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  taskComplete: [taskId: number]
  taskRefresh: [taskId: number]
  refreshTasks: []
}>()

const refreshTimer = ref<NodeJS.Timeout | null>(null)

// 计算属性
const totalCount = computed(() => props.tasks.length)
const completedCount = computed(() => props.tasks.filter(task => task.completed).length)
const completionRate = computed(() => {
  if (totalCount.value === 0) return 0
  return Math.round((completedCount.value / totalCount.value) * 100)
})

// 按任务状态排序：可完成 > 进行中 > 已完成
const sortedTasks = computed(() => {
  return [...props.tasks].sort((a, b) => {
    // 已完成的排在最后
    if (a.completed && !b.completed) return 1
    if (!a.completed && b.completed) return -1
    
    // 可完成的排在最前
    const aCanComplete = !a.completed && (a.currentProgress || 0) >= a.targetValue
    const bCanComplete = !b.completed && (b.currentProgress || 0) >= b.targetValue
    
    if (aCanComplete && !bCanComplete) return -1
    if (!aCanComplete && bCanComplete) return 1
    
    // 其他按进度排序
    const aProgress = (a.currentProgress || 0) / a.targetValue
    const bProgress = (b.currentProgress || 0) / b.targetValue
    
    return bProgress - aProgress
  })
})

// 下次刷新时间
const nextRefreshTime = computed(() => {
  const dailyTasks = props.tasks.filter(task => task.refreshType === 'daily')
  if (dailyTasks.length === 0) return null
  
  // 找到最近的刷新时间
  const minRefreshTime = Math.min(
    ...dailyTasks
      .filter(task => task.refreshCountdown && task.refreshCountdown > 0)
      .map(task => task.refreshCountdown!)
  )
  
  if (minRefreshTime === Infinity) return null
  
  return formatCountdown(minRefreshTime)
})

// 方法
const handleTaskComplete = (taskId: number) => {
  emit('taskComplete', taskId)
}

const handleTaskRefresh = (taskId: number) => {
  emit('taskRefresh', taskId)
}

const startRefreshTimer = () => {
  // 每分钟检查一次任务刷新
  refreshTimer.value = setInterval(() => {
    const hasRefreshableTasks = props.tasks.some(task => 
      task.refreshCountdown && task.refreshCountdown <= 60
    )
    
    if (hasRefreshableTasks) {
      emit('refreshTasks')
    }
  }, 60000)
}

const stopRefreshTimer = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

// 生命周期
onMounted(() => {
  startRefreshTimer()
})

onUnmounted(() => {
  stopRefreshTimer()
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.tasks-list {
  .tasks-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;
    padding-bottom: $spacing-md;
    border-bottom: 1px solid $border-color-light;
    
    h3 {
      margin: 0;
      color: $text-color-primary;
    }
    
    .tasks-summary {
      display: flex;
      gap: $spacing-lg;
    }
  }
  
  .tasks-container {
    min-height: 300px;
    
    .empty-container {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 300px;
    }
    
    .tasks-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: $spacing-lg;
      
      .task-item {
        animation: fadeInUp 0.3s ease;
      }
    }
  }
  
  .refresh-notice {
    margin-top: $spacing-lg;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 移动端适配
@media (max-width: $screen-md) {
  .tasks-list {
    .tasks-header {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-md;
      
      .tasks-summary {
        width: 100%;
        justify-content: space-around;
      }
    }
    
    .tasks-grid {
      grid-template-columns: 1fr !important;
    }
  }
}

@media (max-width: $screen-sm) {
  .tasks-list {
    .tasks-summary {
      flex-direction: column;
      gap: $spacing-sm;
    }
  }
}
</style>
