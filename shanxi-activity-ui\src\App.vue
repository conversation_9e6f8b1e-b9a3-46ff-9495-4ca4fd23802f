<script setup lang="ts">
import { onMounted, provide } from 'vue'
import MainLayout from '@/components/layout/MainLayout.vue'
import { useUserStore } from '@/stores'

const userStore = useUserStore()

// 初始化用户信息
onMounted(async () => {
  try {
    await userStore.fetchUserInfo()
  } catch (error) {
    console.error('Failed to initialize user info:', error)
  }
})
</script>

<template>
  <div id="app">
    <MainLayout />
  </div>
</template>

<style>
#app {
  height: 100vh;
  overflow: hidden;
}
</style>
