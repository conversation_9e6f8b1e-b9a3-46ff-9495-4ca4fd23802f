<template>
  <div class="header-nav">
    <div class="header-left">
      <!-- 菜单切换按钮 -->
      <el-button type="text" class="menu-toggle" @click="toggleSidebar">
        <el-icon size="20">
          <Menu v-if="!isCollapsed" />
          <Expand v-else />
        </el-icon>
      </el-button>

      <!-- Logo和系统名称 -->
      <div class="logo-section">
        <img src="/favicon.ico" alt="Logo" class="logo" />
        <span class="system-name">陕西平台活动工具</span>
      </div>
    </div>

    <div class="header-right">
      <!-- 通宝余额 -->
      <div class="tongbao-balance" v-if="userInfo">
        <el-icon class="tongbao-icon">
          <Coin />
        </el-icon>
        <span class="balance-text">{{ formatTongbao(tongbaoBalance) }}</span>
      </div>

      <!-- 用户信息 -->
      <el-dropdown class="user-dropdown" @command="handleUserCommand">
        <div class="user-info">
          <el-avatar :size="32" :src="userInfo?.avatar" class="user-avatar">
            <el-icon><User /></el-icon>
          </el-avatar>
          <span class="user-name">{{ userInfo?.nickname || "用户" }}</span>
          <el-icon class="dropdown-icon">
            <ArrowDown />
          </el-icon>
        </div>

        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              个人信息
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              设置
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              返回游戏
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, ref } from "vue";
import {
  Menu,
  Expand,
  User,
  Coin,
  ArrowDown,
  Setting,
  SwitchButton,
} from "@element-plus/icons-vue";
import { useUserStore } from "@/stores";
import { formatTongbao } from "@/utils";
import { ElMessage } from "element-plus";

// 定义props来接收父组件传递的方法
const props = defineProps<{
  toggleSidebar?: () => void;
  isCollapsed?: boolean;
}>();

// 使用props或提供默认值
const toggleSidebar = props.toggleSidebar || (() => {});
const isCollapsed = props.isCollapsed || false;

// 用户状态
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);
const tongbaoBalance = computed(() => userStore.tongbaoBalance);

// 处理用户下拉菜单命令
const handleUserCommand = (command: string) => {
  switch (command) {
    case "profile":
      ElMessage.info("个人信息功能开发中");
      break;
    case "settings":
      ElMessage.info("设置功能开发中");
      break;
    case "logout":
      // 返回游戏系统
      if (window.parent && window.parent !== window) {
        // 如果在iframe中，通知父窗口
        window.parent.postMessage({ type: "RETURN_TO_GAME" }, "*");
      } else {
        ElMessage.info("即将返回游戏系统");
      }
      break;
  }
};
</script>

<style lang="scss" scoped>
.header-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 $spacing-lg;
}

.header-left {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.menu-toggle {
  padding: $spacing-sm;
  color: $text-color-primary;

  &:hover {
    background-color: $background-color-light;
  }
}

.logo-section {
  display: flex;
  align-items: center;
  gap: $spacing-sm;

  .logo {
    width: 32px;
    height: 32px;
  }

  .system-name {
    font-size: $font-size-lg;
    font-weight: 600;
    color: $text-color-primary;
  }
}

.header-right {
  display: flex;
  align-items: center;
  gap: $spacing-lg;
}

.tongbao-balance {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  padding: $spacing-xs $spacing-sm;
  background: linear-gradient(135deg, #ffd700 0%, #ffa500 100%);
  border-radius: $border-radius-base;
  color: white;
  font-weight: 600;

  .tongbao-icon {
    font-size: 16px;
  }

  .balance-text {
    font-size: $font-size-sm;
  }
}

.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-base;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: $background-color-light;
  }

  .user-avatar {
    border: 2px solid $border-color-light;
  }

  .user-name {
    font-size: $font-size-sm;
    color: $text-color-primary;
    font-weight: 500;
  }

  .dropdown-icon {
    font-size: 12px;
    color: $text-color-secondary;
  }
}

// 移动端适配
@media (max-width: $screen-sm) {
  .header-nav {
    padding: 0 $spacing-md;
  }

  .logo-section {
    .system-name {
      display: none;
    }
  }

  .tongbao-balance {
    .balance-text {
      display: none;
    }
  }

  .user-info {
    .user-name {
      display: none;
    }
  }
}
</style>
