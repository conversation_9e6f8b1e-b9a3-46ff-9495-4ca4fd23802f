<template>
  <div class="main-layout">
    <el-container>
      <!-- 顶部导航栏 -->
      <el-header class="layout-header">
        <HeaderNav
          :toggle-sidebar="toggleSidebar"
          :is-collapsed="isCollapsed"
        />
      </el-header>

      <el-container>
        <!-- 移动端遮罩层 -->
        <div
          v-if="isMobile && !isCollapsed"
          class="sidebar-overlay"
          @click="toggleSidebar"
        ></div>

        <!-- 侧边栏 -->
        <el-aside
          :width="sidebarWidth"
          class="layout-sidebar"
          :class="{ 'is-collapsed': isCollapsed }"
        >
          <SidebarNav :is-collapsed="isCollapsed" />
        </el-aside>

        <!-- 主内容区 -->
        <el-main class="layout-main">
          <div class="main-content">
            <router-view />
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import HeaderNav from "./HeaderNav.vue";
import SidebarNav from "./SidebarNav.vue";

// 响应式状态
const isCollapsed = ref(false);
const isMobile = ref(false);

// 计算属性
const sidebarWidth = computed(() => {
  if (isMobile.value) {
    return isCollapsed.value ? "0px" : "240px";
  }
  return isCollapsed.value ? "80px" : "240px";
});

// 检查是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
  if (isMobile.value) {
    isCollapsed.value = true;
  }
};

// 窗口大小变化处理
const handleResize = () => {
  checkMobile();
};

// 切换侧边栏
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value;
};

// 生命周期
onMounted(() => {
  checkMobile();
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});

// 不需要defineExpose，因为我们通过props传递
</script>

<style lang="scss" scoped>
.main-layout {
  height: 100vh;
  overflow: hidden;
}

.layout-header {
  background: $background-color-white;
  border-bottom: 1px solid $border-color-light;
  padding: 0;
  height: $layout-header-height;
  line-height: $layout-header-height;
  box-shadow: $box-shadow-card;
  z-index: $z-index-sticky;
}

.layout-sidebar {
  background: $background-color-white;
  border-right: 1px solid $border-color-light;
  transition: width 0.3s ease;
  overflow: hidden;

  &.is-collapsed {
    .sidebar-nav {
      :deep(.el-menu--vertical) {
        .el-menu-item,
        .el-sub-menu__title {
          padding-left: 20px !important;

          .menu-text {
            display: none;
          }
        }

        .el-sub-menu {
          .el-menu-item {
            padding-left: 40px !important;
          }
        }
      }
    }
  }
}

.layout-main {
  background: $background-color-base;
  padding: 0;
  overflow: auto;
}

.main-content {
  padding: $spacing-lg;
  min-height: calc(100vh - #{$layout-header-height});
}

.sidebar-overlay {
  position: fixed;
  top: $layout-header-height;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: $z-index-modal - 1;
  backdrop-filter: blur(2px);
}

// 移动端适配
@media (max-width: $screen-sm) {
  .layout-sidebar {
    position: fixed;
    top: $layout-header-height;
    left: 0;
    height: calc(100vh - #{$layout-header-height});
    z-index: $z-index-modal;
    box-shadow: $box-shadow-base;

    &.is-collapsed {
      transform: translateX(-100%);
    }

    &:not(.is-collapsed) {
      transform: translateX(0);
    }
  }

  .layout-main {
    margin-left: 0 !important;
  }

  .main-content {
    padding: $spacing-md;
  }
}

// 平板适配
@media (min-width: $screen-sm) and (max-width: $screen-md) {
  .layout-sidebar {
    width: 200px;

    &.is-collapsed {
      width: 60px;
    }
  }

  .main-content {
    padding: $spacing-md $spacing-lg;
  }
}

// 大屏适配
@media (min-width: $screen-lg) {
  .main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: $spacing-lg $spacing-xl;
  }
}
</style>
