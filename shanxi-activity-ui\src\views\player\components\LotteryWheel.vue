<template>
  <div class="lottery-wheel">
    <div class="wheel-container" :class="containerClass">
      <!-- 转盘主体 -->
      <div class="wheel-main" :style="wheelStyle" @click="handleClick">
        <svg class="wheel-svg" :width="wheelSize" :height="wheelSize" viewBox="0 0 400 400">
          <!-- 转盘扇形 -->
          <g class="wheel-sectors">
            <path
              v-for="(sector, index) in sectors"
              :key="index"
              :d="sector.path"
              :fill="sector.color"
              :stroke="sector.stroke"
              :stroke-width="2"
              class="wheel-sector"
              :class="{ 'is-winning': sector.isWinning }"
            />
          </g>
          
          <!-- 奖品文字 -->
          <g class="wheel-texts">
            <text
              v-for="(sector, index) in sectors"
              :key="`text-${index}`"
              :x="sector.textX"
              :y="sector.textY"
              :transform="sector.textTransform"
              class="sector-text"
              text-anchor="middle"
              dominant-baseline="middle"
            >
              {{ sector.text }}
            </text>
          </g>
          
          <!-- 中心圆 -->
          <circle
            cx="200"
            cy="200"
            r="30"
            fill="url(#centerGradient)"
            stroke="#FFD700"
            stroke-width="3"
            class="wheel-center"
          />
          
          <!-- 渐变定义 -->
          <defs>
            <radialGradient id="centerGradient" cx="50%" cy="50%" r="50%">
              <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#FFA500;stop-opacity:1" />
            </radialGradient>
          </defs>
        </svg>
        
        <!-- 指针 -->
        <div class="wheel-pointer">
          <div class="pointer-arrow"></div>
        </div>
        
        <!-- 中心按钮 -->
        <div class="wheel-button" :class="buttonClass">
          <el-icon class="button-icon" size="20">
            <component :is="buttonIcon" />
          </el-icon>
          <span class="button-text">{{ buttonText }}</span>
        </div>
      </div>
      
      <!-- 光效 -->
      <div class="wheel-glow" v-if="isGlowing"></div>
    </div>
    
    <!-- 状态信息 -->
    <div class="wheel-status">
      <div class="status-text">{{ statusText }}</div>
      <div class="chances-info" v-if="remainingChances !== undefined">
        <el-icon><Trophy /></el-icon>
        <span>剩余 {{ remainingChances }} 次</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Play, Loading, Trophy } from '@element-plus/icons-vue'
import type { Reward } from '@/types'

interface Props {
  rewards: Reward[]
  disabled?: boolean
  loading?: boolean
  remainingChances?: number
  size?: number
  winningIndex?: number
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  loading: false,
  size: 300
})

const emit = defineEmits<{
  click: []
  spinComplete: [index: number]
}>()

const isSpinning = ref(false)
const currentRotation = ref(0)
const isGlowing = ref(false)

// 计算属性
const wheelSize = computed(() => props.size)

const canClick = computed(() => {
  return !props.disabled && 
         !props.loading && 
         !isSpinning.value && 
         (props.remainingChances === undefined || props.remainingChances > 0)
})

const containerClass = computed(() => [
  {
    'can-click': canClick.value,
    'is-disabled': props.disabled,
    'is-loading': props.loading,
    'is-spinning': isSpinning.value
  }
])

const buttonClass = computed(() => [
  {
    'is-disabled': !canClick.value,
    'is-spinning': isSpinning.value
  }
])

const buttonIcon = computed(() => {
  if (isSpinning.value || props.loading) return Loading
  return Play
})

const buttonText = computed(() => {
  if (isSpinning.value) return '转动中'
  if (props.loading) return '抽奖中'
  if (props.disabled) return '无法抽奖'
  return '开始'
})

const statusText = computed(() => {
  if (isSpinning.value) return '转盘转动中，请稍候...'
  if (props.loading) return '正在为您抽取奖品...'
  if (props.disabled) return '活动未开始或已结束'
  if (props.remainingChances === 0) return '今日抽奖次数已用完'
  return '点击中心按钮开始抽奖'
})

const wheelStyle = computed(() => ({
  transform: `rotate(${currentRotation.value}deg)`,
  transition: isSpinning.value ? 'transform 3s cubic-bezier(0.23, 1, 0.32, 1)' : 'none'
}))

// 计算扇形
const sectors = computed(() => {
  const sectorCount = props.rewards.length || 8
  const sectorAngle = 360 / sectorCount
  const radius = 180
  const centerX = 200
  const centerY = 200
  
  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
    '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'
  ]
  
  return props.rewards.map((reward, index) => {
    const startAngle = index * sectorAngle - 90
    const endAngle = (index + 1) * sectorAngle - 90
    
    const startAngleRad = (startAngle * Math.PI) / 180
    const endAngleRad = (endAngle * Math.PI) / 180
    
    const x1 = centerX + radius * Math.cos(startAngleRad)
    const y1 = centerY + radius * Math.sin(startAngleRad)
    const x2 = centerX + radius * Math.cos(endAngleRad)
    const y2 = centerY + radius * Math.sin(endAngleRad)
    
    const largeArcFlag = sectorAngle > 180 ? 1 : 0
    
    const path = [
      `M ${centerX} ${centerY}`,
      `L ${x1} ${y1}`,
      `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
      'Z'
    ].join(' ')
    
    // 文字位置
    const textAngle = startAngle + sectorAngle / 2
    const textAngleRad = (textAngle * Math.PI) / 180
    const textRadius = radius * 0.7
    const textX = centerX + textRadius * Math.cos(textAngleRad)
    const textY = centerY + textRadius * Math.sin(textAngleRad)
    
    return {
      path,
      color: colors[index % colors.length],
      stroke: '#FFFFFF',
      text: reward.rewardName || `奖品${index + 1}`,
      textX,
      textY,
      textTransform: `rotate(${textAngle}, ${textX}, ${textY})`,
      isWinning: props.winningIndex === index
    }
  })
})

// 方法
const handleClick = () => {
  if (!canClick.value) return
  
  startSpin()
  emit('click')
}

const startSpin = () => {
  if (isSpinning.value) return
  
  isSpinning.value = true
  isGlowing.value = true
  
  // 计算目标角度
  const sectorCount = props.rewards.length || 8
  const sectorAngle = 360 / sectorCount
  const targetIndex = props.winningIndex ?? Math.floor(Math.random() * sectorCount)
  const targetAngle = targetIndex * sectorAngle + sectorAngle / 2
  
  // 多转几圈增加效果
  const spins = 5
  const finalRotation = currentRotation.value + spins * 360 + (360 - targetAngle)
  
  currentRotation.value = finalRotation
  
  // 转动完成
  setTimeout(() => {
    isSpinning.value = false
    isGlowing.value = false
    emit('spinComplete', targetIndex)
  }, 3000)
}

// 监听winningIndex变化
watch(() => props.winningIndex, (newIndex) => {
  if (newIndex !== undefined && isSpinning.value) {
    // 如果正在转动且有中奖索引，更新目标角度
    const sectorCount = props.rewards.length || 8
    const sectorAngle = 360 / sectorCount
    const targetAngle = newIndex * sectorAngle + sectorAngle / 2
    const spins = 5
    const finalRotation = currentRotation.value + spins * 360 + (360 - targetAngle)
    currentRotation.value = finalRotation
  }
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.lottery-wheel {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-lg;
  
  .wheel-container {
    position: relative;
    cursor: pointer;
    
    &.can-click:hover {
      .wheel-main {
        transform: scale(1.02);
      }
    }
    
    &.is-disabled {
      cursor: not-allowed;
      opacity: 0.6;
    }
    
    &.is-spinning {
      cursor: wait;
    }
    
    .wheel-main {
      position: relative;
      transition: transform 0.3s ease;
      border-radius: 50%;
      overflow: hidden;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
      
      .wheel-svg {
        display: block;
        
        .wheel-sector {
          transition: opacity 0.3s ease;
          
          &.is-winning {
            filter: brightness(1.2);
            stroke: #FFD700;
            stroke-width: 4;
          }
        }
        
        .sector-text {
          font-size: 12px;
          font-weight: 600;
          fill: #FFFFFF;
          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
          pointer-events: none;
        }
        
        .wheel-center {
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }
      }
      
      .wheel-pointer {
        position: absolute;
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 10;
        
        .pointer-arrow {
          width: 0;
          height: 0;
          border-left: 15px solid transparent;
          border-right: 15px solid transparent;
          border-top: 30px solid #FF4444;
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }
      }
      
      .wheel-button {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        border: 3px solid #FFFFFF;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        z-index: 10;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        
        &:hover:not(.is-disabled) {
          transform: translate(-50%, -50%) scale(1.1);
        }
        
        &.is-disabled {
          cursor: not-allowed;
          opacity: 0.6;
        }
        
        &.is-spinning {
          .button-icon {
            animation: spin 1s linear infinite;
          }
        }
        
        .button-icon {
          color: #FFFFFF;
          margin-bottom: 2px;
        }
        
        .button-text {
          font-size: 10px;
          font-weight: 600;
          color: #FFFFFF;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
      }
    }
    
    .wheel-glow {
      position: absolute;
      top: -20px;
      left: -20px;
      right: -20px;
      bottom: -20px;
      background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, transparent 70%);
      border-radius: 50%;
      animation: glow 1s ease-in-out infinite alternate;
      z-index: -1;
    }
  }
  
  .wheel-status {
    text-align: center;
    
    .status-text {
      font-size: $font-size-sm;
      color: $text-color-secondary;
      margin-bottom: $spacing-sm;
    }
    
    .chances-info {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: $spacing-xs;
      color: $primary-color;
      font-size: $font-size-sm;
      font-weight: 500;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes glow {
  from { opacity: 0.3; }
  to { opacity: 0.6; }
}
</style>
