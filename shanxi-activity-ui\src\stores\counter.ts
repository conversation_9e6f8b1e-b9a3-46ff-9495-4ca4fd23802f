import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { UserInfo, UserRole } from '@/types'
import { userApi } from '@/api'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<UserInfo | null>(null)
  const isLoggedIn = ref(false)

  // 计算属性
  const userRole = computed(() => userInfo.value?.role || 'player')
  const isAllianceLeader = computed(() => userRole.value === 'alliance_leader')
  const isPartner = computed(() => userRole.value === 'partner')
  const isPlayer = computed(() => userRole.value === 'player')
  const canManageActivity = computed(() => isAllianceLeader.value || isPartner.value)
  const tongbaoBalance = computed(() => userInfo.value?.tongbaoBalance || 0)

  // 方法
  async function fetchUserInfo() {
    try {
      const info = await userApi.getCurrentUserInfo()
      userInfo.value = info
      isLoggedIn.value = true
      return info
    } catch (error) {
      console.error('Failed to fetch user info:', error)
      throw error
    }
  }

  function updateTongbaoBalance(newBalance: number) {
    if (userInfo.value) {
      userInfo.value.tongbaoBalance = newBalance
    }
  }

  function logout() {
    userInfo.value = null
    isLoggedIn.value = false
  }

  function hasRole(role: UserRole): boolean {
    return userRole.value === role
  }

  function hasAnyRole(roles: UserRole[]): boolean {
    return roles.includes(userRole.value)
  }

  return {
    // 状态
    userInfo,
    isLoggedIn,
    // 计算属性
    userRole,
    isAllianceLeader,
    isPartner,
    isPlayer,
    canManageActivity,
    tongbaoBalance,
    // 方法
    fetchUserInfo,
    updateTongbaoBalance,
    logout,
    hasRole,
    hasAnyRole
  }
})
