<template>
  <div class="activity-config">
    <!-- 步骤条 -->
    <div class="steps-container">
      <el-steps :active="currentStep" align-center>
        <el-step title="基础配置" description="设置活动基本信息" />
        <el-step title="奖励配置" description="配置奖励内容和概率" />
        <el-step title="任务配置" description="设置参与任务" />
      </el-steps>
    </div>

    <!-- 步骤内容 -->
    <div class="step-content">
      <!-- 步骤1：基础配置 -->
      <div v-if="currentStep === 0" class="step-panel">
        <h2 class="step-title">活动基础配置</h2>
        <div class="form-grid">
          <div class="form-item">
            <label class="form-label">活动名</label>
            <el-input
              v-model="formData.name"
              placeholder="最多6个字"
              size="large"
              maxlength="6"
              show-word-limit
            />
          </div>

          <div class="form-item">
            <label class="form-label">活动类型</label>
            <el-select
              v-model="formData.type"
              placeholder="盲盒活动"
              size="large"
              style="width: 100%"
            >
              <el-option label="盲盒活动" value="blind_box" />
            </el-select>
          </div>

          <div class="form-item full-width">
            <label class="form-label">活动通宝</label>
            <el-input
              v-model="formData.tongbao"
              placeholder="请输入活动通宝数量"
              size="large"
            />
          </div>

          <div class="form-item">
            <label class="form-label">开始时间</label>
            <input
              v-model="formData.startTime"
              type="datetime-local"
              class="time-input"
              placeholder="开始时间"
            />
          </div>

          <div class="form-item">
            <label class="form-label">结束时间</label>
            <input
              v-model="formData.endTime"
              type="datetime-local"
              class="time-input"
              placeholder="结束时间"
            />
          </div>
        </div>
      </div>

      <!-- 步骤2：奖励配置 -->
      <div v-if="currentStep === 1" class="step-panel">
        <h2 class="step-title">奖励配置</h2>
        <div class="rewards-config">
          <div
            v-for="(reward, index) in formData.rewards"
            :key="index"
            class="reward-item"
          >
            <div class="reward-grid">
              <div class="form-item">
                <label class="form-label">奖励名</label>
                <el-input
                  v-model="reward.name"
                  placeholder="奖励名"
                  size="large"
                />
              </div>
              <div class="form-item">
                <label class="form-label">权重(%)</label>
                <el-input
                  v-model="reward.probability"
                  placeholder="权重"
                  size="large"
                />
              </div>
              <div class="form-item">
                <label class="form-label">类型</label>
                <el-select
                  v-model="reward.type"
                  placeholder="类型"
                  size="large"
                  style="width: 100%"
                >
                  <el-option label="通宝" value="tongbao" />
                  <el-option label="实物" value="physical" />
                </el-select>
              </div>
              <div class="form-item">
                <label class="form-label">奖励通宝</label>
                <el-input
                  v-model="reward.value"
                  placeholder="奖励通宝"
                  size="large"
                />
              </div>
              <div class="form-item">
                <label class="form-label">份数</label>
                <el-input
                  v-model="reward.quantity"
                  placeholder="份数"
                  size="large"
                />
              </div>
              <div class="form-item">
                <el-button
                  type="danger"
                  circle
                  @click="removeReward(index)"
                  size="large"
                  :disabled="formData.rewards.length <= 1"
                >
                  <el-icon><Minus /></el-icon>
                </el-button>
              </div>
            </div>
          </div>

          <el-button
            type="primary"
            circle
            @click="addReward"
            class="add-btn"
            size="large"
          >
            <el-icon><Plus /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 步骤3：任务配置 -->
      <div v-if="currentStep === 2" class="step-panel">
        <h2 class="step-title">任务配置</h2>
        <div class="tasks-config">
          <div
            v-for="(task, index) in formData.tasks"
            :key="index"
            class="task-item"
          >
            <div class="task-grid">
              <div class="form-item">
                <label class="form-label">任务类型</label>
                <el-select
                  v-model="task.type"
                  placeholder="任务类型"
                  size="large"
                  style="width: 100%"
                >
                  <el-option label="游戏局数" value="game_rounds" />
                  <el-option label="登录" value="login" />
                  <el-option label="贡献" value="contribution" />
                </el-select>
              </div>
              <div class="form-item">
                <label class="form-label">任务数值</label>
                <el-input
                  v-model="task.target"
                  placeholder="任务数值"
                  size="large"
                />
              </div>
              <div class="form-item">
                <label class="form-label">奖励次数</label>
                <el-input
                  v-model="task.reward"
                  placeholder="奖励次数"
                  size="large"
                />
              </div>
              <div class="form-item">
                <label class="form-label">刷新时间</label>
                <el-select
                  v-model="task.refreshType"
                  placeholder="刷新时间"
                  size="large"
                  style="width: 100%"
                >
                  <el-option label="每天" value="daily" />
                  <el-option label="每周" value="weekly" />
                  <el-option label="永不" value="never" />
                </el-select>
              </div>
              <div class="form-item">
                <el-button
                  type="danger"
                  circle
                  @click="removeTask(index)"
                  size="large"
                  :disabled="formData.tasks.length <= 1"
                >
                  <el-icon><Minus /></el-icon>
                </el-button>
              </div>
            </div>
          </div>

          <el-button
            type="primary"
            circle
            @click="addTask"
            class="add-btn"
            size="large"
          >
            <el-icon><Plus /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="actions">
      <el-button
        v-if="currentStep > 0"
        size="large"
        @click="prevStep"
        class="action-btn"
      >
        上一步
      </el-button>

      <el-button
        v-if="currentStep < 2"
        type="primary"
        size="large"
        @click="nextStep"
        class="action-btn"
      >
        下一步
      </el-button>

      <el-button
        v-if="currentStep === 2"
        type="primary"
        size="large"
        @click="confirmConfig"
        class="action-btn"
      >
        确认创建
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRouter } from "vue-router";
import { Plus, Minus } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";

const router = useRouter();

// 当前步骤
const currentStep = ref(0);

// 表单数据
const formData = ref({
  name: "",
  type: "blind_box",
  tongbao: "",
  startTime: "",
  endTime: "",
  rewards: [
    { name: "", probability: "", type: "tongbao", value: "", quantity: "" },
  ],
  tasks: [
    { type: "game_rounds", target: "", reward: "", refreshType: "daily" },
  ],
});

// 添加奖励
const addReward = () => {
  formData.value.rewards.push({
    name: "",
    probability: "",
    type: "tongbao",
    value: "",
    quantity: "",
  });
};

// 删除奖励
const removeReward = (index: number) => {
  if (formData.value.rewards.length > 1) {
    formData.value.rewards.splice(index, 1);
  }
};

// 添加任务
const addTask = () => {
  formData.value.tasks.push({
    type: "game_rounds",
    target: "",
    reward: "",
    refreshType: "daily",
  });
};

// 删除任务
const removeTask = (index: number) => {
  if (formData.value.tasks.length > 1) {
    formData.value.tasks.splice(index, 1);
  }
};

// 下一步
const nextStep = () => {
  if (validateCurrentStep()) {
    currentStep.value++;
  }
};

// 上一步
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
};

// 验证当前步骤
const validateCurrentStep = () => {
  switch (currentStep.value) {
    case 0:
      if (!formData.value.name) {
        ElMessage.error("请输入活动名称");
        return false;
      }
      if (!formData.value.tongbao) {
        ElMessage.error("请输入活动通宝");
        return false;
      }
      if (!formData.value.startTime || !formData.value.endTime) {
        ElMessage.error("请选择活动时间");
        return false;
      }
      return true;
    case 1:
      for (const reward of formData.value.rewards) {
        if (
          !reward.name ||
          !reward.probability ||
          !reward.value ||
          !reward.quantity
        ) {
          ElMessage.error("请完整填写奖励配置");
          return false;
        }
      }
      return true;
    case 2:
      for (const task of formData.value.tasks) {
        if (!task.target || !task.reward) {
          ElMessage.error("请完整填写任务配置");
          return false;
        }
      }
      return true;
    default:
      return true;
  }
};

// 确认创建
const confirmConfig = () => {
  if (validateCurrentStep()) {
    ElMessage.success("活动创建成功！");
    router.push("/participate/1");
  }
};
</script>

<style lang="scss" scoped>
.activity-config {
  min-height: 100vh;
  padding: 20px;
  background-color: #f5f5f5;

  // 移动端横屏优化
  @media screen and (orientation: landscape) and (max-height: 600px) {
    padding: 10px;
    min-height: auto;
  }
}

.steps-container {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  @media screen and (orientation: landscape) and (max-height: 600px) {
    padding: 15px;
    margin-bottom: 15px;
  }
}

.step-content {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-height: 70vh;
  overflow-y: auto;

  @media screen and (orientation: landscape) and (max-height: 600px) {
    padding: 20px;
    max-height: 60vh;
    margin-bottom: 15px;
  }
}

.step-title {
  text-align: center;
  font-size: 24px;
  color: #333;
  margin-bottom: 30px;
  font-weight: 600;

  @media screen and (orientation: landscape) and (max-height: 600px) {
    font-size: 20px;
    margin-bottom: 20px;
  }
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;

  @media screen and (orientation: landscape) and (max-height: 600px) {
    gap: 15px;
  }

  .full-width {
    grid-column: 1 / -1;
  }
}

.reward-grid,
.task-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 15px;
  align-items: end;

  @media screen and (orientation: landscape) and (max-height: 600px) {
    gap: 10px;
  }
}

.form-item {
  display: flex;
  flex-direction: column;
}

.form-label {
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;

  @media screen and (orientation: landscape) and (max-height: 600px) {
    margin-bottom: 6px;
    font-size: 12px;
  }
}

.time-input {
  width: 100%;
  height: 40px;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
  background-color: #fff;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

  &:focus {
    outline: none;
    border-color: #409eff;
  }

  &:hover {
    border-color: #c0c4cc;
  }

  @media screen and (orientation: landscape) and (max-height: 600px) {
    height: 36px;
    padding: 6px 10px;
    font-size: 12px;
  }
}

.reward-item,
.task-item {
  margin-bottom: 20px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;

  @media screen and (orientation: landscape) and (max-height: 600px) {
    margin-bottom: 15px;
    padding: 15px;
  }
}

.add-btn {
  margin-top: 15px;
  width: 50px;
  height: 50px;

  @media screen and (orientation: landscape) and (max-height: 600px) {
    width: 40px;
    height: 40px;
  }
}

.actions {
  display: flex;
  justify-content: center;
  gap: 20px;

  @media screen and (orientation: landscape) and (max-height: 600px) {
    gap: 15px;
  }
}

.action-btn {
  padding: 12px 30px;
  font-size: 16px;
  border-radius: 25px;
  min-width: 120px;

  @media screen and (orientation: landscape) and (max-height: 600px) {
    padding: 10px 25px;
    font-size: 14px;
    min-width: 100px;
  }
}

// Element Plus 组件大小调整
:deep(.el-input__inner) {
  @media screen and (orientation: landscape) and (max-height: 600px) {
    height: 36px !important;
  }
}

:deep(.el-select) {
  @media screen and (orientation: landscape) and (max-height: 600px) {
    .el-input__inner {
      height: 36px !important;
    }
  }
}

:deep(.el-date-editor) {
  @media screen and (orientation: landscape) and (max-height: 600px) {
    .el-input__inner {
      height: 36px !important;
    }
  }
}

:deep(.el-button--large) {
  @media screen and (orientation: landscape) and (max-height: 600px) {
    padding: 8px 15px;
    font-size: 14px;
  }
}
</style>
