import { type ExtractPropTypes } from 'vue';
import type { SearchShape } from './types';
declare const searchProps: {
    id: StringConstructor;
    name: StringConstructor;
    leftIcon: StringConstructor;
    rightIcon: StringConstructor;
    autofocus: BooleanConstructor;
    clearable: BooleanConstructor;
    maxlength: (NumberConstructor | StringConstructor)[];
    formatter: import("vue").PropType<(value: string) => string>;
    clearIcon: {
        type: import("vue").PropType<string>;
        default: string;
    };
    modelValue: {
        type: (NumberConstructor | StringConstructor)[];
        default: string;
    };
    inputAlign: import("vue").PropType<import("../field").FieldTextAlign>;
    placeholder: StringConstructor;
    autocomplete: StringConstructor;
    errorMessage: StringConstructor;
    clearTrigger: {
        type: import("vue").PropType<import("../field").FieldClearTrigger>;
        default: import("../field").FieldClearTrigger;
    };
    formatTrigger: {
        type: import("vue").PropType<import("../field").FieldFormatTrigger>;
        default: import("../field").FieldFormatTrigger;
    };
    error: {
        type: BooleanConstructor;
        default: null;
    };
    disabled: {
        type: BooleanConstructor;
        default: null;
    };
    readonly: {
        type: BooleanConstructor;
        default: null;
    };
} & {
    label: StringConstructor;
    shape: {
        type: import("vue").PropType<SearchShape>;
        default: SearchShape;
    };
    leftIcon: {
        type: import("vue").PropType<string>;
        default: string;
    };
    clearable: {
        type: BooleanConstructor;
        default: true;
    };
    actionText: StringConstructor;
    background: StringConstructor;
    showAction: BooleanConstructor;
};
export declare type SearchProps = ExtractPropTypes<typeof searchProps>;
declare const _default: import("vue").DefineComponent<{
    id: StringConstructor;
    name: StringConstructor;
    leftIcon: StringConstructor;
    rightIcon: StringConstructor;
    autofocus: BooleanConstructor;
    clearable: BooleanConstructor;
    maxlength: (NumberConstructor | StringConstructor)[];
    formatter: import("vue").PropType<(value: string) => string>;
    clearIcon: {
        type: import("vue").PropType<string>;
        default: string;
    };
    modelValue: {
        type: (NumberConstructor | StringConstructor)[];
        default: string;
    };
    inputAlign: import("vue").PropType<import("../field").FieldTextAlign>;
    placeholder: StringConstructor;
    autocomplete: StringConstructor;
    errorMessage: StringConstructor;
    clearTrigger: {
        type: import("vue").PropType<import("../field").FieldClearTrigger>;
        default: import("../field").FieldClearTrigger;
    };
    formatTrigger: {
        type: import("vue").PropType<import("../field").FieldFormatTrigger>;
        default: import("../field").FieldFormatTrigger;
    };
    error: {
        type: BooleanConstructor;
        default: null;
    };
    disabled: {
        type: BooleanConstructor;
        default: null;
    };
    readonly: {
        type: BooleanConstructor;
        default: null;
    };
} & {
    label: StringConstructor;
    shape: {
        type: import("vue").PropType<SearchShape>;
        default: SearchShape;
    };
    leftIcon: {
        type: import("vue").PropType<string>;
        default: string;
    };
    clearable: {
        type: BooleanConstructor;
        default: true;
    };
    actionText: StringConstructor;
    background: StringConstructor;
    showAction: BooleanConstructor;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("search" | "clear" | "focus" | "blur" | "click-input" | "click-left-icon" | "click-right-icon" | "update:modelValue" | "cancel")[], "search" | "clear" | "focus" | "blur" | "click-input" | "click-left-icon" | "click-right-icon" | "update:modelValue" | "cancel", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<ExtractPropTypes<{
    id: StringConstructor;
    name: StringConstructor;
    leftIcon: StringConstructor;
    rightIcon: StringConstructor;
    autofocus: BooleanConstructor;
    clearable: BooleanConstructor;
    maxlength: (NumberConstructor | StringConstructor)[];
    formatter: import("vue").PropType<(value: string) => string>;
    clearIcon: {
        type: import("vue").PropType<string>;
        default: string;
    };
    modelValue: {
        type: (NumberConstructor | StringConstructor)[];
        default: string;
    };
    inputAlign: import("vue").PropType<import("../field").FieldTextAlign>;
    placeholder: StringConstructor;
    autocomplete: StringConstructor;
    errorMessage: StringConstructor;
    clearTrigger: {
        type: import("vue").PropType<import("../field").FieldClearTrigger>;
        default: import("../field").FieldClearTrigger;
    };
    formatTrigger: {
        type: import("vue").PropType<import("../field").FieldFormatTrigger>;
        default: import("../field").FieldFormatTrigger;
    };
    error: {
        type: BooleanConstructor;
        default: null;
    };
    disabled: {
        type: BooleanConstructor;
        default: null;
    };
    readonly: {
        type: BooleanConstructor;
        default: null;
    };
} & {
    label: StringConstructor;
    shape: {
        type: import("vue").PropType<SearchShape>;
        default: SearchShape;
    };
    leftIcon: {
        type: import("vue").PropType<string>;
        default: string;
    };
    clearable: {
        type: BooleanConstructor;
        default: true;
    };
    actionText: StringConstructor;
    background: StringConstructor;
    showAction: BooleanConstructor;
}>> & {
    onFocus?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    onClear?: ((...args: any[]) => any) | undefined;
    "onClick-input"?: ((...args: any[]) => any) | undefined;
    "onClick-left-icon"?: ((...args: any[]) => any) | undefined;
    "onClick-right-icon"?: ((...args: any[]) => any) | undefined;
    "onUpdate:modelValue"?: ((...args: any[]) => any) | undefined;
    onCancel?: ((...args: any[]) => any) | undefined;
    onSearch?: ((...args: any[]) => any) | undefined;
}, {
    autofocus: boolean;
    disabled: boolean;
    shape: SearchShape;
    leftIcon: string;
    clearable: boolean;
    clearIcon: string;
    modelValue: string | number;
    clearTrigger: import("../field").FieldClearTrigger;
    formatTrigger: import("../field").FieldFormatTrigger;
    error: boolean;
    readonly: boolean;
    showAction: boolean;
}>;
export default _default;
