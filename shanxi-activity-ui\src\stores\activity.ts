import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { 
  Activity, 
  ActivityDetail, 
  CreateActivityRequest,
  ActivityListParams,
  PaginationResponse 
} from '@/types'
import { activityApi } from '@/api'

export const useActivityStore = defineStore('activity', () => {
  // 状态
  const activities = ref<Activity[]>([])
  const currentActivity = ref<ActivityDetail | null>(null)
  const loading = ref(false)
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(10)

  // 计算属性
  const hasActivities = computed(() => activities.value.length > 0)
  const totalPages = computed(() => Math.ceil(total.value / pageSize.value))

  // 方法
  async function fetchActivities(params: ActivityListParams) {
    loading.value = true
    try {
      const response = await activityApi.getActivityList(params)
      activities.value = response.items
      total.value = response.total
      currentPage.value = response.page
      return response
    } catch (error) {
      console.error('Failed to fetch activities:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  async function fetchActivityDetail(activityId: number) {
    loading.value = true
    try {
      const activity = await activityApi.getActivityDetail(activityId)
      currentActivity.value = activity
      return activity
    } catch (error) {
      console.error('Failed to fetch activity detail:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  async function createActivity(data: CreateActivityRequest) {
    loading.value = true
    try {
      const activity = await activityApi.createActivity(data)
      // 添加到活动列表
      activities.value.unshift(activity)
      return activity
    } catch (error) {
      console.error('Failed to create activity:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  async function closeActivity(activityId: number) {
    loading.value = true
    try {
      await activityApi.closeActivity(activityId)
      // 更新本地状态
      const activity = activities.value.find(a => a.id === activityId)
      if (activity) {
        activity.status = 'closed'
      }
      if (currentActivity.value?.id === activityId) {
        currentActivity.value.status = 'closed'
      }
    } catch (error) {
      console.error('Failed to close activity:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  function updateActivityInList(updatedActivity: Activity) {
    const index = activities.value.findIndex(a => a.id === updatedActivity.id)
    if (index !== -1) {
      activities.value[index] = updatedActivity
    }
  }

  function clearCurrentActivity() {
    currentActivity.value = null
  }

  function clearActivities() {
    activities.value = []
    total.value = 0
    currentPage.value = 1
  }

  return {
    // 状态
    activities,
    currentActivity,
    loading,
    total,
    currentPage,
    pageSize,
    // 计算属性
    hasActivities,
    totalPages,
    // 方法
    fetchActivities,
    fetchActivityDetail,
    createActivity,
    closeActivity,
    updateActivityInList,
    clearCurrentActivity,
    clearActivities
  }
})
