import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import type { ApiResponse } from '@/types'

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 从游戏系统获取用户ID并添加到请求头
    const gameUserId = getGameUserId()
    if (gameUserId) {
      config.headers['X-Game-User-Id'] = gameUserId
    }

    return config
  },
  (error) => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response

    // 检查业务状态码
    if (data.code !== 200) {
      console.error('API Error:', data.message)
      // 这里可以根据不同的错误码进行不同的处理
      return Promise.reject(new Error(data.message || 'API Error'))
    }

    return response
  },
  (error) => {
    console.error('Response error:', error)

    // 处理网络错误
    if (error.code === 'ECONNABORTED') {
      console.error('Request timeout')
    } else if (error.response) {
      // 服务器返回错误状态码
      const { status, data } = error.response
      console.error(`HTTP Error ${status}:`, data?.message || error.message)
    } else {
      // 网络错误或其他错误
      console.error('Network Error:', error.message)
    }

    return Promise.reject(error)
  }
)

// 获取游戏用户ID的函数（需要根据实际游戏系统接口实现）
function getGameUserId(): string | null {
  // 这里应该从游戏系统获取当前用户ID
  // 临时返回测试用户ID
  return localStorage.getItem('gameUserId') || 'test_user_001'
}

// 通用请求方法
export const http = {
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return request.get<ApiResponse<T>>(url, config).then(res => res.data.data)
  },

  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return request.post<ApiResponse<T>>(url, data, config).then(res => res.data.data)
  },

  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return request.put<ApiResponse<T>>(url, data, config).then(res => res.data.data)
  },

  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return request.delete<ApiResponse<T>>(url, config).then(res => res.data.data)
  }
}

export default request
