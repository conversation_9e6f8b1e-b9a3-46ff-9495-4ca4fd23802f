<template>
  <div class="sidebar-nav">
    <el-menu
      :default-active="activeMenu"
      :collapse="isCollapsed"
      :unique-opened="true"
      router
      class="sidebar-menu"
    >
      <!-- 首页 -->
      <el-menu-item index="/dashboard">
        <el-icon>
          <House />
        </el-icon>
        <span class="menu-text">首页</span>
      </el-menu-item>

      <!-- 管理员/合伙人菜单 -->
      <template v-if="canManageActivity">
        <el-sub-menu index="activity">
          <template #title>
            <el-icon>
              <Trophy />
            </el-icon>
            <span class="menu-text">活动管理</span>
          </template>

          <el-menu-item index="/activity/create">
            <el-icon>
              <Plus />
            </el-icon>
            <span class="menu-text">创建活动</span>
          </el-menu-item>

          <el-menu-item index="/activity/list">
            <el-icon>
              <List />
            </el-icon>
            <span class="menu-text">活动列表</span>
          </el-menu-item>

          <el-menu-item index="/activity/history">
            <el-icon>
              <Clock />
            </el-icon>
            <span class="menu-text">历史活动</span>
          </el-menu-item>
        </el-sub-menu>

        <el-menu-item index="/statistics">
          <el-icon>
            <DataAnalysis />
          </el-icon>
          <span class="menu-text">数据统计</span>
        </el-menu-item>

        <el-menu-item index="/tongbao">
          <el-icon>
            <Coin />
          </el-icon>
          <span class="menu-text">通宝管理</span>
        </el-menu-item>
      </template>

      <!-- 玩家菜单 -->
      <template v-if="isPlayer">
        <el-menu-item index="/player/activities">
          <el-icon>
            <VideoPlay />
          </el-icon>
          <span class="menu-text">我的活动</span>
        </el-menu-item>

        <el-menu-item index="/player/records">
          <el-icon>
            <Medal />
          </el-icon>
          <span class="menu-text">中奖记录</span>
        </el-menu-item>

        <el-menu-item index="/player/tongbao">
          <el-icon>
            <Coin />
          </el-icon>
          <span class="menu-text">通宝记录</span>
        </el-menu-item>
      </template>

      <!-- 盟主专属菜单 -->
      <template v-if="isAllianceLeader">
        <el-sub-menu index="admin">
          <template #title>
            <el-icon>
              <Setting />
            </el-icon>
            <span class="menu-text">系统管理</span>
          </template>

          <el-menu-item index="/admin/overview">
            <el-icon>
              <Monitor />
            </el-icon>
            <span class="menu-text">数据总览</span>
          </el-menu-item>

          <el-menu-item index="/admin/settings">
            <el-icon>
              <Tools />
            </el-icon>
            <span class="menu-text">系统设置</span>
          </el-menu-item>
        </el-sub-menu>
      </template>
    </el-menu>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useRoute } from "vue-router";
import {
  House,
  Trophy,
  Plus,
  List,
  Clock,
  DataAnalysis,
  Coin,
  Medal,
  Setting,
  Monitor,
  Tools,
  VideoPlay,
} from "@element-plus/icons-vue";
import { useUserStore } from "@/stores";

// 定义props
const props = defineProps<{
  isCollapsed?: boolean;
}>();

const isCollapsed = props.isCollapsed || false;

// 路由和用户状态
const route = useRoute();
const userStore = useUserStore();

// 计算属性
const activeMenu = computed(() => route.path);
const canManageActivity = computed(() => userStore.canManageActivity);
const isPlayer = computed(() => userStore.isPlayer);
const isAllianceLeader = computed(() => userStore.isAllianceLeader);
</script>

<style lang="scss" scoped>
@use "sass:color";

.sidebar-nav {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

.sidebar-menu {
  border: none;
  height: 100%;

  .el-menu-item,
  .el-sub-menu__title {
    height: 48px;
    line-height: 48px;
    padding-left: $spacing-lg !important;

    .el-icon {
      margin-right: $spacing-sm;
      font-size: 18px;
    }

    .menu-text {
      font-size: $font-size-sm;
      font-weight: 500;
    }

    &:hover {
      background-color: $background-color-light;
    }

    &.is-active {
      background-color: color.scale($primary-color, $lightness: 45%);
      color: $primary-color;
      border-right: 3px solid $primary-color;

      .el-icon {
        color: $primary-color;
      }
    }
  }

  .el-sub-menu {
    .el-menu-item {
      padding-left: calc(#{$spacing-lg} + #{$spacing-lg}) !important;
      height: 40px;
      line-height: 40px;

      .el-icon {
        font-size: 16px;
      }

      .menu-text {
        font-size: $font-size-sm;
        font-weight: 400;
      }
    }

    .el-sub-menu__title {
      .el-icon {
        color: $text-color-secondary;
      }

      &:hover {
        .el-icon {
          color: $primary-color;
        }
      }
    }

    &.is-opened {
      .el-sub-menu__title {
        background-color: $background-color-light;

        .el-icon {
          color: $primary-color;
        }
      }
    }
  }

  // 折叠状态样式
  &.el-menu--collapse {
    .el-menu-item,
    .el-sub-menu__title {
      padding-left: 20px !important;

      .menu-text {
        display: none;
      }

      .el-icon {
        margin-right: 0;
      }
    }

    .el-sub-menu {
      .el-menu-item {
        padding-left: 20px !important;
      }
    }
  }
}

// 滚动条样式
.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: $border-color-base;
  border-radius: 2px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: $text-color-disabled;
}
</style>
