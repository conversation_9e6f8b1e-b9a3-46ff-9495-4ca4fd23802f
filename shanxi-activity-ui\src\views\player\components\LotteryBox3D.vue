<template>
  <div class="lottery-box-3d">
    <div class="box-container" :class="containerClass" @click="handleClick">
      <div class="box-wrapper">
        <!-- 盒子主体 -->
        <div class="box-main" :class="boxClass">
          <div class="box-face box-front">
            <div class="box-content">
              <el-icon class="box-icon" size="48">
                <Gift />
              </el-icon>
              <div class="box-text">{{ boxText }}</div>
            </div>
          </div>
          
          <div class="box-face box-back">
            <div class="box-pattern"></div>
          </div>
          
          <div class="box-face box-left">
            <div class="box-pattern"></div>
          </div>
          
          <div class="box-face box-right">
            <div class="box-pattern"></div>
          </div>
          
          <div class="box-face box-top">
            <div class="box-lid">
              <div class="lid-handle"></div>
            </div>
          </div>
          
          <div class="box-face box-bottom">
            <div class="box-pattern"></div>
          </div>
        </div>
        
        <!-- 光效 -->
        <div class="box-glow" v-if="isGlowing"></div>
        
        <!-- 粒子效果 -->
        <div class="particles" v-if="showParticles">
          <div 
            v-for="i in 12" 
            :key="i"
            class="particle"
            :style="getParticleStyle(i)"
          ></div>
        </div>
      </div>
      
      <!-- 阴影 -->
      <div class="box-shadow"></div>
    </div>
    
    <!-- 状态提示 -->
    <div class="box-status">
      <div class="status-text" :class="statusClass">
        {{ statusText }}
      </div>
      
      <div class="chances-info" v-if="remainingChances !== undefined">
        <el-icon><Trophy /></el-icon>
        <span>剩余 {{ remainingChances }} 次</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Gift, Trophy } from '@element-plus/icons-vue'

interface Props {
  disabled?: boolean
  loading?: boolean
  remainingChances?: number
  size?: 'small' | 'default' | 'large'
  glowColor?: string
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  loading: false,
  size: 'default',
  glowColor: '#FFD700'
})

const emit = defineEmits<{
  click: []
}>()

const isAnimating = ref(false)
const isGlowing = ref(false)
const showParticles = ref(false)

// 计算属性
const canClick = computed(() => {
  return !props.disabled && 
         !props.loading && 
         !isAnimating.value && 
         (props.remainingChances === undefined || props.remainingChances > 0)
})

const containerClass = computed(() => [
  `box-${props.size}`,
  {
    'can-click': canClick.value,
    'is-disabled': props.disabled,
    'is-loading': props.loading
  }
])

const boxClass = computed(() => [
  {
    'is-animating': isAnimating.value,
    'is-opening': props.loading,
    'is-glowing': isGlowing.value
  }
])

const statusClass = computed(() => [
  {
    'status-disabled': props.disabled,
    'status-loading': props.loading,
    'status-ready': canClick.value
  }
])

const boxText = computed(() => {
  if (props.loading) return '抽奖中...'
  if (props.disabled) return '无法抽奖'
  if (props.remainingChances === 0) return '次数用完'
  return '点击抽奖'
})

const statusText = computed(() => {
  if (props.loading) return '正在为您抽取奖品...'
  if (props.disabled) return '活动未开始或已结束'
  if (props.remainingChances === 0) return '今日抽奖次数已用完'
  return '点击盲盒开始抽奖'
})

// 方法
const handleClick = () => {
  if (!canClick.value) return
  
  startAnimation()
  emit('click')
}

const startAnimation = () => {
  isAnimating.value = true
  isGlowing.value = true
  
  // 震动动画
  setTimeout(() => {
    showParticles.value = true
  }, 500)
  
  // 重置动画状态
  setTimeout(() => {
    isAnimating.value = false
    isGlowing.value = false
    showParticles.value = false
  }, 2000)
}

const getParticleStyle = (index: number) => {
  const angle = (360 / 12) * index
  const distance = 60 + Math.random() * 40
  const duration = 1 + Math.random() * 0.5
  
  return {
    '--angle': `${angle}deg`,
    '--distance': `${distance}px`,
    '--duration': `${duration}s`,
    '--delay': `${Math.random() * 0.5}s`
  }
}

// 监听loading状态变化
watch(() => props.loading, (loading) => {
  if (loading) {
    startAnimation()
  }
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.lottery-box-3d {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-lg;
  
  .box-container {
    position: relative;
    perspective: 1000px;
    cursor: pointer;
    transition: transform 0.3s ease;
    
    &.can-click:hover {
      transform: scale(1.05);
    }
    
    &.is-disabled {
      cursor: not-allowed;
      opacity: 0.6;
    }
    
    &.is-loading {
      cursor: wait;
    }
    
    // 尺寸变体
    &.box-small {
      .box-wrapper {
        width: 80px;
        height: 80px;
      }
    }
    
    &.box-large {
      .box-wrapper {
        width: 160px;
        height: 160px;
      }
    }
    
    .box-wrapper {
      position: relative;
      width: 120px;
      height: 120px;
      transform-style: preserve-3d;
      
      .box-main {
        position: relative;
        width: 100%;
        height: 100%;
        transform-style: preserve-3d;
        transition: transform 0.6s ease;
        
        &.is-animating {
          animation: boxShake 0.5s ease-in-out;
        }
        
        &.is-opening {
          animation: boxOpen 2s ease-in-out;
        }
        
        &.is-glowing {
          filter: drop-shadow(0 0 20px v-bind(glowColor));
        }
        
        .box-face {
          position: absolute;
          width: 100%;
          height: 100%;
          border: 2px solid #8B4513;
          background: linear-gradient(135deg, #D2691E 0%, #A0522D 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          
          &.box-front {
            transform: translateZ(60px);
            
            .box-content {
              text-align: center;
              color: white;
              
              .box-icon {
                margin-bottom: $spacing-sm;
                filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
              }
              
              .box-text {
                font-size: $font-size-sm;
                font-weight: 600;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
              }
            }
          }
          
          &.box-back {
            transform: translateZ(-60px) rotateY(180deg);
          }
          
          &.box-left {
            transform: rotateY(-90deg) translateZ(60px);
          }
          
          &.box-right {
            transform: rotateY(90deg) translateZ(60px);
          }
          
          &.box-top {
            transform: rotateX(90deg) translateZ(60px);
            background: linear-gradient(135deg, #CD853F 0%, #8B4513 100%);
            
            .box-lid {
              width: 80%;
              height: 80%;
              background: linear-gradient(135deg, #DAA520 0%, #B8860B 100%);
              border-radius: 4px;
              position: relative;
              
              .lid-handle {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 20px;
                height: 8px;
                background: #FFD700;
                border-radius: 4px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
              }
            }
          }
          
          &.box-bottom {
            transform: rotateX(-90deg) translateZ(60px);
          }
          
          .box-pattern {
            width: 100%;
            height: 100%;
            background-image: 
              repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255, 255, 255, 0.1) 10px,
                rgba(255, 255, 255, 0.1) 20px
              );
          }
        }
      }
      
      .box-glow {
        position: absolute;
        top: -10px;
        left: -10px;
        right: -10px;
        bottom: -10px;
        background: radial-gradient(circle, v-bind(glowColor) 0%, transparent 70%);
        border-radius: 50%;
        opacity: 0.6;
        animation: glow 1s ease-in-out infinite alternate;
        z-index: -1;
      }
      
      .particles {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        
        .particle {
          position: absolute;
          width: 4px;
          height: 4px;
          background: radial-gradient(circle, #FFD700, #FFA500);
          border-radius: 50%;
          animation: particleFloat var(--duration) ease-out var(--delay) forwards;
        }
      }
    }
    
    .box-shadow {
      position: absolute;
      bottom: -20px;
      left: 50%;
      transform: translateX(-50%);
      width: 80%;
      height: 10px;
      background: radial-gradient(ellipse, rgba(0, 0, 0, 0.3) 0%, transparent 70%);
      border-radius: 50%;
    }
  }
  
  .box-status {
    text-align: center;
    
    .status-text {
      font-size: $font-size-sm;
      margin-bottom: $spacing-sm;
      
      &.status-disabled {
        color: $text-color-disabled;
      }
      
      &.status-loading {
        color: $warning-color;
      }
      
      &.status-ready {
        color: $success-color;
        font-weight: 500;
      }
    }
    
    .chances-info {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: $spacing-xs;
      color: $primary-color;
      font-size: $font-size-sm;
      font-weight: 500;
    }
  }
}

@keyframes boxShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px) rotateY(-5deg); }
  75% { transform: translateX(5px) rotateY(5deg); }
}

@keyframes boxOpen {
  0% { transform: rotateY(0deg); }
  50% { transform: rotateY(180deg) scale(1.1); }
  100% { transform: rotateY(360deg); }
}

@keyframes glow {
  from { opacity: 0.4; }
  to { opacity: 0.8; }
}

@keyframes particleFloat {
  0% {
    transform: translate(0, 0) scale(0);
    opacity: 1;
  }
  100% {
    transform: 
      translate(
        calc(cos(var(--angle)) * var(--distance)),
        calc(sin(var(--angle)) * var(--distance))
      ) 
      scale(1);
    opacity: 0;
  }
}
</style>
