export declare const Circle: import("../utils").WithInstall<import("vue").DefineComponent<{
    text: StringConstructor;
    size: (NumberConstructor | StringConstructor)[];
    fill: {
        type: import("vue").PropType<string>;
        default: string;
    };
    rate: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
    };
    speed: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
    };
    color: import("vue").PropType<string | Record<string, string>>;
    clockwise: {
        type: BooleanConstructor;
        default: true;
    };
    layerColor: StringConstructor;
    currentRate: {
        type: NumberConstructor;
        default: number;
    };
    strokeWidth: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
    };
    strokeLinecap: import("vue").PropType<CanvasLineCap>;
    startPosition: {
        type: import("vue").PropType<import("./Circle").CircleStartPosition>;
        default: import("./Circle").CircleStartPosition;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "update:currentRate"[], "update:currentRate", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    text: StringConstructor;
    size: (NumberConstructor | StringConstructor)[];
    fill: {
        type: import("vue").PropType<string>;
        default: string;
    };
    rate: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
    };
    speed: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
    };
    color: import("vue").PropType<string | Record<string, string>>;
    clockwise: {
        type: BooleanConstructor;
        default: true;
    };
    layerColor: StringConstructor;
    currentRate: {
        type: NumberConstructor;
        default: number;
    };
    strokeWidth: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
    };
    strokeLinecap: import("vue").PropType<CanvasLineCap>;
    startPosition: {
        type: import("vue").PropType<import("./Circle").CircleStartPosition>;
        default: import("./Circle").CircleStartPosition;
    };
}>> & {
    "onUpdate:currentRate"?: ((...args: any[]) => any) | undefined;
}, {
    fill: string;
    strokeWidth: string | number;
    rate: string | number;
    speed: string | number;
    clockwise: boolean;
    currentRate: number;
    startPosition: import("./Circle").CircleStartPosition;
}>>;
export default Circle;
export type { CircleProps, CircleStartPosition } from './Circle';
declare module 'vue' {
    interface GlobalComponents {
        VanCircle: typeof Circle;
    }
}
