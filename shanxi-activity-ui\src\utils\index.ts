// 格式化日期时间
export function formatDateTime(dateStr: string): string {
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化倒计时
export function formatCountdown(seconds: number): string {
  if (seconds <= 0) return '已结束'

  const days = Math.floor(seconds / (24 * 3600))
  const hours = Math.floor((seconds % (24 * 3600)) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)

  let result = ''
  if (days > 0) result += `${days}天`
  if (hours > 0) result += `${hours}小时`
  if (minutes > 0) result += `${minutes}分`

  return result || '不足1分钟'
}

// 格式化通宝数量
export function formatTongbao(amount: number): string {
  if (amount >= 10000) {
    return `${(amount / 10000).toFixed(1)}万`
  }
  return amount.toLocaleString()
}

// 计算概率百分比
export function calculateProbability(weight: number, totalWeight: number): number {
  if (totalWeight === 0) return 0
  return Math.round((weight / totalWeight) * 10000) / 100
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout> | null = null

  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let lastTime = 0

  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastTime >= wait) {
      lastTime = now
      func(...args)
    }
  }
}

// 深拷贝
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime()) as T
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as T
  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
  return obj
}

// 生成唯一ID
export function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 验证表单字段
export const validators = {
  required: (value: any) => {
    if (value === null || value === undefined || value === '') {
      return '此字段为必填项'
    }
    return true
  },

  minLength: (min: number) => (value: string) => {
    if (value && value.length < min) {
      return `最少需要${min}个字符`
    }
    return true
  },

  maxLength: (max: number) => (value: string) => {
    if (value && value.length > max) {
      return `最多允许${max}个字符`
    }
    return true
  },

  min: (min: number) => (value: number) => {
    if (value !== null && value !== undefined && value < min) {
      return `最小值为${min}`
    }
    return true
  },

  max: (max: number) => (value: number) => {
    if (value !== null && value !== undefined && value > max) {
      return `最大值为${max}`
    }
    return true
  },

  positive: (value: number) => {
    if (value !== null && value !== undefined && value <= 0) {
      return '必须为正数'
    }
    return true
  }
}

// 获取活动状态文本
export function getActivityStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    not_started: '未开始',
    running: '进行中',
    ended: '已结束',
    closed: '已关闭'
  }
  return statusMap[status] || status
}

// 获取任务类型文本
export function getTaskTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    login: '登录任务',
    game_rounds: '局数任务',
    contribution: '贡献任务'
  }
  return typeMap[type] || type
}

// 获取刷新类型文本
export function getRefreshTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    daily: '每日',
    weekly: '每周',
    never: '不刷新'
  }
  return typeMap[type] || type
}
