declare const name: string, bem: (el?: import("../utils").Mods | undefined, mods?: import("../utils").Mods | undefined) => import("../utils").Mods, t: (path: string, ...args: unknown[]) => any;
export { name, bem, t };
export declare const formatMonthTitle: (date: Date) => any;
export declare function compareMonth(date1: Date, date2: Date): 0 | 1 | -1;
export declare function compareDay(day1: Date, day2: Date): 0 | 1 | -1;
export declare const cloneDate: (date: Date) => Date;
export declare const cloneDates: (dates: Date | Date[]) => Date | Date[];
export declare function getDayByOffset(date: Date, offset: number): Date;
export declare const getPrevDay: (date: Date) => Date;
export declare const getNextDay: (date: Date) => Date;
export declare const getToday: () => Date;
export declare function calcDateNum(date: [Date, Date]): number;
