<template>
  <div class="detail-config">
    <div class="config-container">
      <h1 class="page-title">活动详细配置</h1>
      
      <!-- 奖励配置 -->
      <div class="config-section">
        <h2 class="section-title">奖励配置</h2>
        <div class="rewards-config">
          <div 
            v-for="(reward, index) in rewards" 
            :key="index"
            class="reward-item"
          >
            <el-row :gutter="16" align="middle">
              <el-col :span="4">
                <el-input 
                  v-model="reward.name" 
                  placeholder="奖励名"
                  size="large"
                />
              </el-col>
              <el-col :span="3">
                <el-input 
                  v-model="reward.probability" 
                  placeholder="权重"
                  size="large"
                />
                <span class="unit">%</span>
              </el-col>
              <el-col :span="3">
                <el-select 
                  v-model="reward.type" 
                  placeholder="类型"
                  size="large"
                >
                  <el-option label="通宝" value="tongbao" />
                  <el-option label="实物" value="physical" />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-input 
                  v-model="reward.value" 
                  placeholder="奖励通宝"
                  size="large"
                />
              </el-col>
              <el-col :span="3">
                <el-input 
                  v-model="reward.quantity" 
                  placeholder="份数"
                  size="large"
                />
              </el-col>
              <el-col :span="2">
                <el-button 
                  type="danger" 
                  circle 
                  @click="removeReward(index)"
                  size="large"
                >
                  <el-icon><Minus /></el-icon>
                </el-button>
              </el-col>
            </el-row>
          </div>
          
          <el-button 
            type="primary" 
            circle 
            @click="addReward"
            class="add-reward-btn"
            size="large"
          >
            <el-icon><Plus /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 任务配置 -->
      <div class="config-section">
        <h2 class="section-title">任务配置</h2>
        <div class="tasks-config">
          <div 
            v-for="(task, index) in tasks" 
            :key="index"
            class="task-item"
          >
            <el-row :gutter="16" align="middle">
              <el-col :span="4">
                <el-select 
                  v-model="task.type" 
                  placeholder="任务类型"
                  size="large"
                >
                  <el-option label="游戏局数" value="game_rounds" />
                  <el-option label="登录" value="login" />
                  <el-option label="贡献" value="contribution" />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-input 
                  v-model="task.target" 
                  placeholder="任务数值"
                  size="large"
                />
              </el-col>
              <el-col :span="4">
                <el-input 
                  v-model="task.reward" 
                  placeholder="奖励次数"
                  size="large"
                />
              </el-col>
              <el-col :span="4">
                <el-select 
                  v-model="task.refreshType" 
                  placeholder="任务刷新时间"
                  size="large"
                >
                  <el-option label="每天" value="daily" />
                  <el-option label="每周" value="weekly" />
                  <el-option label="永不" value="never" />
                </el-select>
              </el-col>
              <el-col :span="2">
                <el-button 
                  type="danger" 
                  circle 
                  @click="removeTask(index)"
                  size="large"
                >
                  <el-icon><Minus /></el-icon>
                </el-button>
              </el-col>
            </el-row>
          </div>
          
          <el-button 
            type="primary" 
            circle 
            @click="addTask"
            class="add-task-btn"
            size="large"
          >
            <el-icon><Plus /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 确认按钮 -->
      <div class="actions">
        <el-button 
          type="primary" 
          size="large" 
          @click="confirmConfig"
          class="confirm-btn"
        >
          确认
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Plus, Minus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 奖励配置
const rewards = ref([
  { name: '', probability: '', type: 'tongbao', value: '', quantity: '' }
])

// 任务配置
const tasks = ref([
  { type: 'game_rounds', target: '', reward: '', refreshType: 'daily' }
])

// 添加奖励
const addReward = () => {
  rewards.value.push({ 
    name: '', 
    probability: '', 
    type: 'tongbao', 
    value: '', 
    quantity: '' 
  })
}

// 删除奖励
const removeReward = (index: number) => {
  if (rewards.value.length > 1) {
    rewards.value.splice(index, 1)
  }
}

// 添加任务
const addTask = () => {
  tasks.value.push({ 
    type: 'game_rounds', 
    target: '', 
    reward: '', 
    refreshType: 'daily' 
  })
}

// 删除任务
const removeTask = (index: number) => {
  if (tasks.value.length > 1) {
    tasks.value.splice(index, 1)
  }
}

// 确认配置
const confirmConfig = () => {
  // 验证配置
  const isValid = validateConfig()
  if (!isValid) return
  
  // 保存配置并跳转到参与页面
  ElMessage.success('活动配置完成！')
  router.push('/participate/1')
}

// 验证配置
const validateConfig = () => {
  // 验证奖励配置
  for (const reward of rewards.value) {
    if (!reward.name || !reward.probability || !reward.value || !reward.quantity) {
      ElMessage.error('请完整填写奖励配置')
      return false
    }
  }
  
  // 验证任务配置
  for (const task of tasks.value) {
    if (!task.target || !task.reward) {
      ElMessage.error('请完整填写任务配置')
      return false
    }
  }
  
  return true
}
</script>

<style lang="scss" scoped>
.detail-config {
  min-height: 100vh;
  padding: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.config-container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.page-title {
  text-align: center;
  font-size: 32px;
  color: #333;
  margin-bottom: 40px;
  font-weight: 600;
}

.config-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 20px;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f0f0f0;
}

.reward-item,
.task-item {
  margin-bottom: 16px;
  padding: 20px;
  background: #fafafa;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
}

.unit {
  margin-left: 8px;
  color: #666;
}

.add-reward-btn,
.add-task-btn {
  margin-top: 16px;
  width: 50px;
  height: 50px;
}

.actions {
  text-align: center;
  margin-top: 40px;
}

.confirm-btn {
  padding: 15px 60px;
  font-size: 18px;
  border-radius: 25px;
}
</style>
