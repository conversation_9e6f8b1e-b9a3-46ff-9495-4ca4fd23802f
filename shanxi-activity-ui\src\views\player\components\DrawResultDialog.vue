<template>
  <el-dialog
    v-model="visible"
    title="抽奖结果"
    width="400px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    align-center
    class="draw-result-dialog"
  >
    <div class="result-content" v-if="result">
      <!-- 庆祝动画 -->
      <div class="celebration-animation">
        <div class="fireworks">
          <div class="firework" v-for="i in 6" :key="i"></div>
        </div>
      </div>
      
      <!-- 奖励展示 -->
      <div class="reward-display">
        <div class="reward-icon">
          <el-icon size="64" class="gift-icon">
            <Gift />
          </el-icon>
        </div>
        
        <div class="reward-info">
          <h3 class="congratulations">🎉 恭喜您！</h3>
          <div class="reward-name">{{ result.reward.rewardName }}</div>
          <div class="reward-amount" v-if="result.reward.rewardType === 'tongbao'">
            {{ formatTongbao(result.reward.tongbaoAmount) }}
          </div>
          <div class="reward-description" v-if="result.reward.physicalItem">
            {{ result.reward.physicalItem }}
          </div>
        </div>
      </div>
      
      <!-- 剩余次数 -->
      <div class="remaining-info">
        <el-statistic 
          title="剩余抽奖次数" 
          :value="result.remainingChances"
          :value-style="{ color: '#1890ff', fontSize: '24px' }"
        />
      </div>
      
      <!-- 分享按钮 -->
      <div class="share-section">
        <el-button 
          type="primary" 
          @click="handleShare"
          :icon="Share"
          size="large"
        >
          分享好运
        </el-button>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button 
          v-if="result && result.remainingChances > 0"
          type="primary" 
          @click="handleContinue"
          size="large"
        >
          继续抽奖
        </el-button>
        
        <el-button 
          @click="handleClose"
          size="large"
        >
          {{ result && result.remainingChances > 0 ? '稍后再抽' : '确定' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Gift, Share } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { formatTongbao } from '@/utils'
import type { DrawResult } from '@/types'

interface Props {
  modelValue: boolean
  result: DrawResult | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  close: []
  continue: []
}>()

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 方法
const handleClose = () => {
  visible.value = false
  emit('close')
}

const handleContinue = () => {
  visible.value = false
  emit('continue')
}

const handleShare = () => {
  if (!props.result) return
  
  const shareText = `我在活动中抽中了${props.result.reward.rewardName}！快来参与吧！`
  
  // 尝试使用Web Share API
  if (navigator.share) {
    navigator.share({
      title: '中奖分享',
      text: shareText,
      url: window.location.href
    }).catch(() => {
      // 分享失败，复制到剪贴板
      copyToClipboard(shareText)
    })
  } else {
    // 不支持Web Share API，复制到剪贴板
    copyToClipboard(shareText)
  }
}

const copyToClipboard = (text: string) => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text).then(() => {
      ElMessage.success('分享内容已复制到剪贴板')
    }).catch(() => {
      ElMessage.error('复制失败')
    })
  } else {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      ElMessage.success('分享内容已复制到剪贴板')
    } catch {
      ElMessage.error('复制失败')
    }
    document.body.removeChild(textArea)
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.draw-result-dialog {
  :deep(.el-dialog) {
    border-radius: $border-radius-lg;
    overflow: hidden;
  }
  
  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, $primary-color 0%, lighten($primary-color, 10%) 100%);
    color: white;
    text-align: center;
    padding: $spacing-lg;
    
    .el-dialog__title {
      color: white;
      font-weight: 600;
    }
  }
  
  :deep(.el-dialog__body) {
    padding: $spacing-xl;
  }
  
  .result-content {
    position: relative;
    text-align: center;
    
    .celebration-animation {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;
      z-index: 1;
      
      .fireworks {
        position: relative;
        width: 100%;
        height: 100%;
        
        .firework {
          position: absolute;
          width: 4px;
          height: 4px;
          background: radial-gradient(circle, #FFD700, #FFA500);
          border-radius: 50%;
          animation: firework 2s ease-out infinite;
          
          &:nth-child(1) {
            top: 20%;
            left: 20%;
            animation-delay: 0s;
          }
          
          &:nth-child(2) {
            top: 30%;
            right: 20%;
            animation-delay: 0.3s;
          }
          
          &:nth-child(3) {
            top: 50%;
            left: 10%;
            animation-delay: 0.6s;
          }
          
          &:nth-child(4) {
            top: 40%;
            right: 10%;
            animation-delay: 0.9s;
          }
          
          &:nth-child(5) {
            bottom: 30%;
            left: 30%;
            animation-delay: 1.2s;
          }
          
          &:nth-child(6) {
            bottom: 20%;
            right: 30%;
            animation-delay: 1.5s;
          }
        }
      }
    }
    
    .reward-display {
      position: relative;
      z-index: 2;
      margin-bottom: $spacing-xl;
      
      .reward-icon {
        margin-bottom: $spacing-lg;
        
        .gift-icon {
          color: $warning-color;
          animation: bounce 1s ease-in-out infinite;
        }
      }
      
      .reward-info {
        .congratulations {
          margin: 0 0 $spacing-md 0;
          color: $success-color;
          font-size: $font-size-xl;
          font-weight: 600;
        }
        
        .reward-name {
          font-size: $font-size-lg;
          font-weight: 600;
          color: $text-color-primary;
          margin-bottom: $spacing-sm;
        }
        
        .reward-amount {
          font-size: $font-size-xxl;
          font-weight: 700;
          color: $success-color;
          margin-bottom: $spacing-sm;
        }
        
        .reward-description {
          font-size: $font-size-sm;
          color: $text-color-secondary;
        }
      }
    }
    
    .remaining-info {
      margin-bottom: $spacing-xl;
      padding: $spacing-lg;
      background: $background-color-light;
      border-radius: $border-radius-base;
    }
    
    .share-section {
      margin-bottom: $spacing-lg;
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: center;
    gap: $spacing-md;
  }
}

@keyframes firework {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  50% {
    transform: scale(1);
    opacity: 0.8;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

// 移动端适配
@media (max-width: $screen-sm) {
  .draw-result-dialog {
    :deep(.el-dialog) {
      width: 90% !important;
      margin: 5vh auto;
    }
    
    .dialog-footer {
      flex-direction: column;
      
      .el-button {
        width: 100%;
      }
    }
  }
}
</style>
