import { FormProps } from './Form';
export declare const Form: import("../utils").WithInstall<import("vue").DefineComponent<{
    colon: BooleanConstructor;
    disabled: BooleanConstructor;
    readonly: BooleanConstructor;
    showError: BooleanConstructor;
    labelWidth: (NumberConstructor | StringConstructor)[];
    labelAlign: import("vue").PropType<import("..").FieldTextAlign>;
    inputAlign: import("vue").PropType<import("..").FieldTextAlign>;
    scrollToError: BooleanConstructor;
    validateFirst: BooleanConstructor;
    submitOnEnter: {
        type: BooleanConstructor;
        default: true;
    };
    validateTrigger: {
        type: import("vue").PropType<import("..").FieldValidateTrigger>;
        default: import("..").FieldValidateTrigger;
    };
    showErrorMessage: {
        type: BooleanConstructor;
        default: true;
    };
    errorMessageAlign: import("vue").PropType<import("..").FieldTextAlign>;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("submit" | "failed")[], "submit" | "failed", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    colon: BooleanConstructor;
    disabled: BooleanConstructor;
    readonly: BooleanConstructor;
    showError: BooleanConstructor;
    labelWidth: (NumberConstructor | StringConstructor)[];
    labelAlign: import("vue").PropType<import("..").FieldTextAlign>;
    inputAlign: import("vue").PropType<import("..").FieldTextAlign>;
    scrollToError: BooleanConstructor;
    validateFirst: BooleanConstructor;
    submitOnEnter: {
        type: BooleanConstructor;
        default: true;
    };
    validateTrigger: {
        type: import("vue").PropType<import("..").FieldValidateTrigger>;
        default: import("..").FieldValidateTrigger;
    };
    showErrorMessage: {
        type: BooleanConstructor;
        default: true;
    };
    errorMessageAlign: import("vue").PropType<import("..").FieldTextAlign>;
}>> & {
    onSubmit?: ((...args: any[]) => any) | undefined;
    onFailed?: ((...args: any[]) => any) | undefined;
}, {
    disabled: boolean;
    readonly: boolean;
    colon: boolean;
    showError: boolean;
    scrollToError: boolean;
    validateFirst: boolean;
    submitOnEnter: boolean;
    validateTrigger: import("..").FieldValidateTrigger;
    showErrorMessage: boolean;
}>>;
export default Form;
export type { FormProps };
export type { FormInstance } from './types';
declare module 'vue' {
    interface GlobalComponents {
        VanForm: typeof Form;
    }
}
