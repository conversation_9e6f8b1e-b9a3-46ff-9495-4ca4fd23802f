import { http } from './request'
import type {
  Activity,
  ActivityDetail,
  CreateActivityRequest,
  ActivityListParams,
  PaginationResponse,
  ActivityStatistics
} from '@/types'

// 活动管理API
export const activityApi = {
  // 创建活动
  createActivity(data: CreateActivityRequest): Promise<Activity> {
    return http.post('/activities', data)
  },

  // 获取活动列表
  getActivityList(params: ActivityListParams): Promise<PaginationResponse<Activity>> {
    return http.get('/activities', { params })
  },

  // 获取活动详情
  getActivityDetail(activityId: number): Promise<ActivityDetail> {
    return http.get(`/activities/${activityId}`)
  },

  // 关闭活动
  closeActivity(activityId: number): Promise<void> {
    return http.put(`/activities/${activityId}/close`)
  },

  // 获取活动统计数据
  getActivityStatistics(activityId: number): Promise<ActivityStatistics> {
    return http.get(`/activities/${activityId}/statistics`)
  }
}
