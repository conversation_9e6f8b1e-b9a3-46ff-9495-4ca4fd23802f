// 主题色彩
$primary-color: #1890FF;
$success-color: #52C41A;
$warning-color: #FAAD14;
$error-color: #F5222D;
$info-color: #1890FF;

// 辅助色彩
$text-color-primary: #262626;
$text-color-secondary: #595959;
$text-color-disabled: #BFBFBF;
$text-color-inverse: #FFFFFF;

// 背景色
$background-color-base: #F5F5F5;
$background-color-light: #FAFAFA;
$background-color-white: #FFFFFF;

// 边框色
$border-color-base: #D9D9D9;
$border-color-light: #E8E8E8;
$border-color-split: #F0F0F0;

// 阴影
$box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
$box-shadow-card: 0 1px 2px rgba(0, 0, 0, 0.03), 0 1px 6px rgba(0, 0, 0, 0.03), 0 2px 24px rgba(0, 0, 0, 0.03);

// 圆角
$border-radius-base: 4px;
$border-radius-sm: 2px;
$border-radius-lg: 8px;

// 字体
$font-family: 'Microsoft YaHei', Arial, sans-serif;
$font-size-base: 14px;
$font-size-lg: 16px;
$font-size-sm: 12px;
$font-size-xl: 18px;
$font-size-xxl: 20px;

// 行高
$line-height-base: 1.5;
$line-height-lg: 1.8;
$line-height-sm: 1.2;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;

// 布局
$layout-header-height: 64px;
$layout-sidebar-width: 240px;
$layout-sidebar-collapsed-width: 80px;

// 响应式断点
$screen-xs: 480px;
$screen-sm: 768px;
$screen-md: 1024px;
$screen-lg: 1200px;
$screen-xl: 1600px;

// 节日主题色彩
$spring-festival-primary: #FF4D4F;
$spring-festival-secondary: #FFD700;
$mid-autumn-primary: #FA8C16;
$mid-autumn-secondary: #FAAD14;
$christmas-primary: #52C41A;
$christmas-secondary: #FF4D4F;

// 暗色模式
$dark-bg-color: #141414;
$dark-card-bg-color: #1F1F1F;
$dark-text-color: #FFFFFF;
$dark-border-color: #303030;

// 跑马灯样式
$marquee-bg-gradient: linear-gradient(90deg, #FFD700 0%, #FFA500 100%);
$marquee-text-color: #FFFFFF;
$marquee-height: 40px;

// 抽奖动画
$lottery-box-size: 120px;
$lottery-animation-duration: 2s;

// Z-index层级
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;
