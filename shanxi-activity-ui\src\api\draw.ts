import { http } from './request'
import type {
  DrawChances,
  DrawResult,
  DrawRecord,
  DrawRecordParams,
  MarqueeMessage
} from '@/types'

// 抽奖相关API
export const drawApi = {
  // 获取抽奖次数
  getDrawChances(activityId: number): Promise<DrawChances> {
    return http.get(`/activities/${activityId}/draw-chances`)
  },

  // 执行抽奖
  drawReward(activityId: number): Promise<DrawResult> {
    return http.post(`/activities/${activityId}/draw`)
  },

  // 获取抽奖记录
  getDrawRecords(params: DrawRecordParams): Promise<DrawRecord[]> {
    const { activityId, ...queryParams } = params
    return http.get(`/activities/${activityId}/draw-records`, { params: queryParams })
  },

  // 获取跑马灯消息
  getMarqueeMessages(activityId: number): Promise<MarqueeMessage[]> {
    return http.get(`/activities/${activityId}/marquee`)
  }
}
