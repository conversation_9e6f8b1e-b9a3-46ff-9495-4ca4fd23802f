export declare const Cascader: import("../utils").WithInstall<import("vue").DefineComponent<{
    title: StringConstructor;
    options: {
        type: import("vue").PropType<import("./types").CascaderOption[]>;
        default: () => never[];
    };
    closeable: {
        type: BooleanConstructor;
        default: true;
    };
    swipeable: {
        type: BooleanConstructor;
        default: true;
    };
    closeIcon: {
        type: import("vue").PropType<string>;
        default: string;
    };
    showHeader: {
        type: BooleanConstructor;
        default: true;
    };
    modelValue: (NumberConstructor | StringConstructor)[];
    fieldNames: import("vue").PropType<import("./types").CascaderFieldNames>;
    placeholder: StringConstructor;
    activeColor: StringConstructor;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("update:modelValue" | "change" | "close" | "click-tab" | "finish")[], "update:modelValue" | "change" | "close" | "click-tab" | "finish", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    title: StringConstructor;
    options: {
        type: import("vue").PropType<import("./types").CascaderOption[]>;
        default: () => never[];
    };
    closeable: {
        type: BooleanConstructor;
        default: true;
    };
    swipeable: {
        type: BooleanConstructor;
        default: true;
    };
    closeIcon: {
        type: import("vue").PropType<string>;
        default: string;
    };
    showHeader: {
        type: BooleanConstructor;
        default: true;
    };
    modelValue: (NumberConstructor | StringConstructor)[];
    fieldNames: import("vue").PropType<import("./types").CascaderFieldNames>;
    placeholder: StringConstructor;
    activeColor: StringConstructor;
}>> & {
    onChange?: ((...args: any[]) => any) | undefined;
    "onUpdate:modelValue"?: ((...args: any[]) => any) | undefined;
    onClose?: ((...args: any[]) => any) | undefined;
    "onClick-tab"?: ((...args: any[]) => any) | undefined;
    onFinish?: ((...args: any[]) => any) | undefined;
}, {
    closeIcon: string;
    closeable: boolean;
    options: import("./types").CascaderOption[];
    swipeable: boolean;
    showHeader: boolean;
}>>;
export default Cascader;
export type { CascaderProps } from './Cascader';
export type { CascaderOption, CascaderFieldNames } from './types';
declare module 'vue' {
    interface GlobalComponents {
        VanCascader: typeof Cascader;
    }
}
