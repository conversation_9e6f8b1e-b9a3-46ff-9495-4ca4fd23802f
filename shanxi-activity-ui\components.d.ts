/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BaseButton: typeof import('./src/components/common/BaseButton.vue')['default']
    BaseCard: typeof import('./src/components/common/BaseCard.vue')['default']
    EmptyState: typeof import('./src/components/common/EmptyState.vue')['default']
    ErrorBoundary: typeof import('./src/components/common/ErrorBoundary.vue')['default']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    IconCommunity: typeof import('./src/components/icons/IconCommunity.vue')['default']
    IconDocumentation: typeof import('./src/components/icons/IconDocumentation.vue')['default']
    IconEcosystem: typeof import('./src/components/icons/IconEcosystem.vue')['default']
    IconSupport: typeof import('./src/components/icons/IconSupport.vue')['default']
    IconTooling: typeof import('./src/components/icons/IconTooling.vue')['default']
    LoadingSpinner: typeof import('./src/components/common/LoadingSpinner.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    StatusTag: typeof import('./src/components/common/StatusTag.vue')['default']
    TheWelcome: typeof import('./src/components/TheWelcome.vue')['default']
    VanDatetimePicker: typeof import('vant/es')['DatetimePicker']
    VanField: typeof import('vant/es')['Field']
    WelcomeItem: typeof import('./src/components/WelcomeItem.vue')['default']
  }
}
