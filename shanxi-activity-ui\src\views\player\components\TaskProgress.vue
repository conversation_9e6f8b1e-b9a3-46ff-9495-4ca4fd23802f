<template>
  <div class="task-progress">
    <div class="progress-header">
      <div class="progress-info">
        <span class="current">{{ currentProgress }}</span>
        <span class="separator">/</span>
        <span class="target">{{ targetValue }}</span>
      </div>
      <div class="progress-percentage">
        {{ progressPercentage }}%
      </div>
    </div>
    
    <div class="progress-bar-container">
      <el-progress 
        :percentage="progressPercentage"
        :color="progressColor"
        :stroke-width="strokeWidth"
        :show-text="false"
        :class="progressClass"
      />
    </div>
    
    <div class="progress-footer" v-if="showFooter">
      <div class="progress-status">
        <el-icon class="status-icon">
          <component :is="statusIcon" />
        </el-icon>
        <span class="status-text">{{ statusText }}</span>
      </div>
      
      <div class="progress-reward" v-if="rewardChances">
        <el-icon class="reward-icon">
          <Trophy />
        </el-icon>
        <span class="reward-text">{{ rewardChances }}次</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  CircleCheck, 
  Clock, 
  VideoPlay, 
  Trophy 
} from '@element-plus/icons-vue'

interface Props {
  currentProgress: number
  targetValue: number
  rewardChances?: number
  completed?: boolean
  strokeWidth?: number
  showFooter?: boolean
  size?: 'small' | 'default' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  currentProgress: 0,
  targetValue: 1,
  strokeWidth: 8,
  showFooter: true,
  size: 'default'
})

// 计算属性
const progressPercentage = computed(() => {
  if (props.targetValue === 0) return 0
  return Math.min(100, Math.round((props.currentProgress / props.targetValue) * 100))
})

const isCompleted = computed(() => {
  return props.completed || props.currentProgress >= props.targetValue
})

const canComplete = computed(() => {
  return !props.completed && props.currentProgress >= props.targetValue
})

const progressColor = computed(() => {
  if (isCompleted.value) return '#52c41a'
  if (canComplete.value) return '#52c41a'
  if (progressPercentage.value >= 80) return '#fa8c16'
  if (progressPercentage.value >= 50) return '#1890ff'
  return '#d9d9d9'
})

const progressClass = computed(() => {
  return [
    `progress-${props.size}`,
    {
      'is-completed': isCompleted.value,
      'can-complete': canComplete.value
    }
  ]
})

const statusIcon = computed(() => {
  if (isCompleted.value) return CircleCheck
  if (canComplete.value) return CircleCheck
  return props.currentProgress > 0 ? VideoPlay : Clock
})

const statusText = computed(() => {
  if (props.completed) return '已完成'
  if (canComplete.value) return '可领取'
  if (props.currentProgress > 0) return '进行中'
  return '未开始'
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.task-progress {
  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-sm;
    
    .progress-info {
      display: flex;
      align-items: baseline;
      gap: $spacing-xs;
      
      .current {
        font-size: $font-size-lg;
        font-weight: 600;
        color: $text-color-primary;
      }
      
      .separator {
        color: $text-color-secondary;
      }
      
      .target {
        font-size: $font-size-base;
        color: $text-color-secondary;
      }
    }
    
    .progress-percentage {
      font-size: $font-size-sm;
      font-weight: 500;
      color: $text-color-secondary;
    }
  }
  
  .progress-bar-container {
    margin-bottom: $spacing-sm;
    
    :deep(.el-progress) {
      &.progress-small {
        .el-progress-bar__outer {
          height: 4px !important;
        }
      }
      
      &.progress-large {
        .el-progress-bar__outer {
          height: 12px !important;
        }
      }
      
      &.is-completed {
        .el-progress-bar__outer {
          background-color: lighten($success-color, 40%);
        }
      }
      
      &.can-complete {
        .el-progress-bar__inner {
          animation: pulse 1.5s ease-in-out infinite;
        }
      }
      
      .el-progress-bar__outer {
        border-radius: 10px;
        background-color: $background-color-light;
      }
      
      .el-progress-bar__inner {
        border-radius: 10px;
        transition: all 0.3s ease;
      }
    }
  }
  
  .progress-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .progress-status {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
      
      .status-icon {
        font-size: 14px;
        color: $text-color-secondary;
      }
      
      .status-text {
        font-size: $font-size-sm;
        color: $text-color-secondary;
      }
    }
    
    .progress-reward {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
      
      .reward-icon {
        font-size: 14px;
        color: $warning-color;
      }
      
      .reward-text {
        font-size: $font-size-sm;
        color: $warning-color;
        font-weight: 500;
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

// 不同尺寸的样式
.task-progress {
  &.size-small {
    .progress-header {
      .progress-info {
        .current {
          font-size: $font-size-base;
        }
      }
    }
  }
  
  &.size-large {
    .progress-header {
      .progress-info {
        .current {
          font-size: $font-size-xl;
        }
      }
      
      .progress-percentage {
        font-size: $font-size-base;
      }
    }
  }
}
</style>
