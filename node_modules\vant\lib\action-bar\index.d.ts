export declare const ActionBar: import("../utils").WithInstall<import("vue").DefineComponent<{
    safeAreaInsetBottom: {
        type: BooleanConstructor;
        default: true;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    safeAreaInsetBottom: {
        type: BooleanConstructor;
        default: true;
    };
}>>, {
    safeAreaInsetBottom: boolean;
}>>;
export default ActionBar;
export type { ActionBarProps } from './ActionBar';
declare module 'vue' {
    interface GlobalComponents {
        VanActionBar: typeof ActionBar;
    }
}
