import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { 
  Draw<PERSON>hances, 
  DrawResult, 
  DrawRecord,
  MarqueeMessage 
} from '@/types'
import { drawApi } from '@/api'

export const useDrawStore = defineStore('draw', () => {
  // 状态
  const drawChances = ref<DrawChances | null>(null)
  const drawRecords = ref<DrawRecord[]>([])
  const marqueeMessages = ref<MarqueeMessage[]>([])
  const isDrawing = ref(false)
  const lastDrawResult = ref<DrawResult | null>(null)

  // 计算属性
  const canDraw = computed(() => {
    return drawChances.value && drawChances.value.remainingChances > 0 && !isDrawing.value
  })

  const hasRecords = computed(() => drawRecords.value.length > 0)

  // 方法
  async function fetchDrawChances(activityId: number) {
    try {
      const chances = await drawApi.getDrawChances(activityId)
      drawChances.value = chances
      return chances
    } catch (error) {
      console.error('Failed to fetch draw chances:', error)
      throw error
    }
  }

  async function executeDrawReward(activityId: number) {
    if (!canDraw.value) {
      throw new Error('无法进行抽奖')
    }

    isDrawing.value = true
    try {
      const result = await drawApi.drawReward(activityId)
      lastDrawResult.value = result
      
      // 更新抽奖次数
      if (drawChances.value) {
        drawChances.value.remainingChances = result.remainingChances
        drawChances.value.usedChances = drawChances.value.totalChances - result.remainingChances
      }

      return result
    } catch (error) {
      console.error('Failed to draw reward:', error)
      throw error
    } finally {
      isDrawing.value = false
    }
  }

  async function fetchDrawRecords(activityId: number, type: 'all' | 'my' = 'all', limit = 50) {
    try {
      const records = await drawApi.getDrawRecords({ activityId, type, limit })
      drawRecords.value = records
      return records
    } catch (error) {
      console.error('Failed to fetch draw records:', error)
      throw error
    }
  }

  async function fetchMarqueeMessages(activityId: number) {
    try {
      const messages = await drawApi.getMarqueeMessages(activityId)
      marqueeMessages.value = messages
      return messages
    } catch (error) {
      console.error('Failed to fetch marquee messages:', error)
      throw error
    }
  }

  function addNewRecord(record: DrawRecord) {
    drawRecords.value.unshift(record)
    // 限制记录数量
    if (drawRecords.value.length > 50) {
      drawRecords.value = drawRecords.value.slice(0, 50)
    }
  }

  function addNewMarqueeMessage(message: MarqueeMessage) {
    marqueeMessages.value.unshift(message)
    // 限制消息数量
    if (marqueeMessages.value.length > 10) {
      marqueeMessages.value = marqueeMessages.value.slice(0, 10)
    }
  }

  function clearDrawData() {
    drawChances.value = null
    drawRecords.value = []
    marqueeMessages.value = []
    lastDrawResult.value = null
    isDrawing.value = false
  }

  function clearLastDrawResult() {
    lastDrawResult.value = null
  }

  return {
    // 状态
    drawChances,
    drawRecords,
    marqueeMessages,
    isDrawing,
    lastDrawResult,
    // 计算属性
    canDraw,
    hasRecords,
    // 方法
    fetchDrawChances,
    executeDrawReward,
    fetchDrawRecords,
    fetchMarqueeMessages,
    addNewRecord,
    addNewMarqueeMessage,
    clearDrawData,
    clearLastDrawResult
  }
})
