{"explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"tsconfig.json": "tsconfig.*.json, env.d.ts", "vite.config.*": "jsconfig*, vitest.config.*, cypress.config.*, playwright.config.*", "package.json": "package-lock.json, pnpm*, .yarnrc*, yarn*, .eslint*, eslint*, .oxlint*, oxlint*, .prettier*, prettier*, .editorconfig"}, "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "vetur.validation.template": false, "vetur.validation.script": false, "vetur.validation.style": false, "typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always"}