import { type PropType } from 'vue';
export declare type KeyType = '' | 'delete' | 'extra' | 'close';
declare const _default: import("vue").DefineComponent<{
    type: PropType<KeyType>;
    text: (NumberConstructor | StringConstructor)[];
    color: StringConstructor;
    wider: BooleanConstructor;
    large: BooleanConstructor;
    loading: BooleanConstructor;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "press"[], "press", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    type: PropType<KeyType>;
    text: (NumberConstructor | StringConstructor)[];
    color: StringConstructor;
    wider: BooleanConstructor;
    large: BooleanConstructor;
    loading: BooleanConstructor;
}>> & {
    onPress?: ((...args: any[]) => any) | undefined;
}, {
    large: boolean;
    loading: boolean;
    wider: boolean;
}>;
export default _default;
