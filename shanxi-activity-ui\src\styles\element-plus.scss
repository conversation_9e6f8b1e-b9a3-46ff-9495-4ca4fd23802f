@use 'sass:color';
@use './variables.scss' as *;

// Element Plus 主题定制
:root {
  --el-color-primary: #{$primary-color};
  --el-color-success: #{$success-color};
  --el-color-warning: #{$warning-color};
  --el-color-danger: #{$error-color};
  --el-color-info: #{$info-color};

  --el-border-radius-base: #{$border-radius-base};
  --el-border-radius-small: #{$border-radius-sm};
  --el-border-radius-round: #{$border-radius-lg};

  --el-font-family: #{$font-family};
  --el-font-size-base: #{$font-size-base};
  --el-font-size-small: #{$font-size-sm};
  --el-font-size-large: #{$font-size-lg};

  --el-box-shadow: #{$box-shadow-base};
  --el-box-shadow-light: #{$box-shadow-card};
}

// 自定义组件样式
.el-card {
  border: none;
  box-shadow: $box-shadow-card;

  .el-card__header {
    border-bottom: 1px solid $border-color-light;
    padding: $spacing-md $spacing-lg;
    font-weight: 600;
  }

  .el-card__body {
    padding: $spacing-lg;
  }
}

.el-button {
  border-radius: $border-radius-base;
  font-weight: 500;

  &.el-button--primary {
    background: $primary-color;
    border-color: $primary-color;

    &:hover {
      background: #{color.scale($primary-color, $lightness: 22.0779220779%)};
      border-color: #{color.scale($primary-color, $lightness: 22.0779220779%)};
    }

    &:active {
      background: #{color.scale($primary-color, $lightness: -9.1397849462%)};
      border-color: #{color.scale($primary-color, $lightness: -9.1397849462%)};
    }
  }

  &.el-button--success {
    background: $success-color;
    border-color: $success-color;
  }

  &.el-button--warning {
    background: $warning-color;
    border-color: $warning-color;
  }

  &.el-button--danger {
    background: $error-color;
    border-color: $error-color;
  }
}

.el-input {
  .el-input__wrapper {
    border-radius: $border-radius-base;
    box-shadow: 0 0 0 1px $border-color-base inset;

    &:hover {
      box-shadow: 0 0 0 1px $primary-color inset;
    }

    &.is-focus {
      box-shadow: 0 0 0 1px $primary-color inset;
    }
  }
}

.el-select {
  .el-select__wrapper {
    border-radius: $border-radius-base;
  }
}

.el-table {
  border-radius: $border-radius-base;
  overflow: hidden;

  .el-table__header {
    th {
      background-color: $background-color-light;
      color: $text-color-primary;
      font-weight: 600;
    }
  }

  .el-table__row {
    &:hover {
      background-color: $background-color-light;
    }
  }
}

.el-pagination {
  .el-pagination__jump {
    margin-left: $spacing-md;
  }

  .btn-next,
  .btn-prev {
    border-radius: $border-radius-base;
  }

  .el-pager li {
    border-radius: $border-radius-base;
    margin: 0 2px;

    &.is-active {
      background-color: $primary-color;
      color: white;
    }
  }
}

.el-dialog {
  border-radius: $border-radius-lg;

  .el-dialog__header {
    border-bottom: 1px solid $border-color-light;
    padding: $spacing-lg;
  }

  .el-dialog__body {
    padding: $spacing-lg;
  }

  .el-dialog__footer {
    border-top: 1px solid $border-color-light;
    padding: $spacing-md $spacing-lg;
  }
}

.el-drawer {
  .el-drawer__header {
    border-bottom: 1px solid $border-color-light;
    padding: $spacing-lg;
    margin-bottom: 0;
  }

  .el-drawer__body {
    padding: $spacing-lg;
  }
}

.el-steps {
  .el-step__title {
    font-weight: 500;
  }

  .el-step.is-process .el-step__title {
    color: $primary-color;
    font-weight: 600;
  }
}

.el-progress {
  .el-progress-bar__outer {
    border-radius: $border-radius-lg;
  }

  .el-progress-bar__inner {
    border-radius: $border-radius-lg;
  }
}

.el-tag {
  border-radius: $border-radius-base;

  &.el-tag--success {
    background-color: #{color.scale($success-color, $lightness: 66.67%)};
    border-color: #{color.scale($success-color, $lightness: 50%)};
    color: $success-color;
  }

  &.el-tag--warning {
    background-color: #{color.scale($warning-color, $lightness: 66.67%)};
    border-color: #{color.scale($warning-color, $lightness: 50%)};
    color: $warning-color;
  }

  &.el-tag--danger {
    background-color: #{color.scale($error-color, $lightness: 66.67%)};
    border-color: #{color.scale($error-color, $lightness: 50%)};
    color: $error-color;
  }
}

.el-alert {
  border-radius: $border-radius-base;

  .el-alert__title {
    font-weight: 600;
  }
}

// 自定义loading样式
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);

  .el-loading-spinner {
    .el-loading-text {
      color: $primary-color;
      font-weight: 500;
    }
  }
}
