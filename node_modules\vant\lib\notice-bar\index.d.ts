import { NoticeBarProps } from './NoticeBar';
export declare const NoticeBar: import("../utils").WithInstall<import("vue").DefineComponent<{
    text: StringConstructor;
    mode: import("vue").PropType<import("./types").NoticeBarMode>;
    color: StringConstructor;
    delay: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
    };
    speed: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
    };
    leftIcon: StringConstructor;
    wrapable: BooleanConstructor;
    background: StringConstructor;
    scrollable: {
        type: import("vue").PropType<boolean | null>;
        default: null;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("close" | "replay")[], "close" | "replay", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    text: StringConstructor;
    mode: import("vue").PropType<import("./types").NoticeBarMode>;
    color: StringConstructor;
    delay: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
    };
    speed: {
        type: (NumberConstructor | StringConstructor)[];
        default: number;
    };
    leftIcon: StringConstructor;
    wrapable: BooleanConstructor;
    background: StringConstructor;
    scrollable: {
        type: import("vue").PropType<boolean | null>;
        default: null;
    };
}>> & {
    onClose?: ((...args: any[]) => any) | undefined;
    onReplay?: ((...args: any[]) => any) | undefined;
}, {
    scrollable: boolean | null;
    speed: string | number;
    delay: string | number;
    wrapable: boolean;
}>>;
export default NoticeBar;
export type { NoticeBarProps };
export type { NoticeBarMode, NoticeBarInstance } from './types';
declare module 'vue' {
    interface GlobalComponents {
        VanNoticeBar: typeof NoticeBar;
    }
}
