import { type PropType, type CSSProperties, type ExtractPropTypes } from 'vue';
import { LoadingType } from '../loading';
import type { ToastType, ToastPosition } from './types';
declare const toastProps: {
    icon: StringConstructor;
    show: BooleanConstructor;
    type: {
        type: PropType<ToastType>;
        default: ToastType;
    };
    overlay: BooleanConstructor;
    message: (NumberConstructor | StringConstructor)[];
    iconSize: (NumberConstructor | StringConstructor)[];
    duration: {
        type: NumberConstructor;
        default: number;
    };
    position: {
        type: PropType<ToastPosition>;
        default: ToastPosition;
    };
    className: PropType<unknown>;
    iconPrefix: StringConstructor;
    transition: {
        type: PropType<string>;
        default: string;
    };
    loadingType: PropType<LoadingType>;
    forbidClick: BooleanConstructor;
    overlayClass: PropType<unknown>;
    overlayStyle: PropType<CSSProperties>;
    closeOnClick: BooleanConstructor;
    closeOnClickOverlay: BooleanConstructor;
};
export declare type ToastProps = ExtractPropTypes<typeof toastProps>;
declare const _default: import("vue").DefineComponent<{
    icon: StringConstructor;
    show: BooleanConstructor;
    type: {
        type: PropType<ToastType>;
        default: ToastType;
    };
    overlay: BooleanConstructor;
    message: (NumberConstructor | StringConstructor)[];
    iconSize: (NumberConstructor | StringConstructor)[];
    duration: {
        type: NumberConstructor;
        default: number;
    };
    position: {
        type: PropType<ToastPosition>;
        default: ToastPosition;
    };
    className: PropType<unknown>;
    iconPrefix: StringConstructor;
    transition: {
        type: PropType<string>;
        default: string;
    };
    loadingType: PropType<LoadingType>;
    forbidClick: BooleanConstructor;
    overlayClass: PropType<unknown>;
    overlayStyle: PropType<CSSProperties>;
    closeOnClick: BooleanConstructor;
    closeOnClickOverlay: BooleanConstructor;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "update:show"[], "update:show", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<ExtractPropTypes<{
    icon: StringConstructor;
    show: BooleanConstructor;
    type: {
        type: PropType<ToastType>;
        default: ToastType;
    };
    overlay: BooleanConstructor;
    message: (NumberConstructor | StringConstructor)[];
    iconSize: (NumberConstructor | StringConstructor)[];
    duration: {
        type: NumberConstructor;
        default: number;
    };
    position: {
        type: PropType<ToastPosition>;
        default: ToastPosition;
    };
    className: PropType<unknown>;
    iconPrefix: StringConstructor;
    transition: {
        type: PropType<string>;
        default: string;
    };
    loadingType: PropType<LoadingType>;
    forbidClick: BooleanConstructor;
    overlayClass: PropType<unknown>;
    overlayStyle: PropType<CSSProperties>;
    closeOnClick: BooleanConstructor;
    closeOnClickOverlay: BooleanConstructor;
}>> & {
    "onUpdate:show"?: ((...args: any[]) => any) | undefined;
}, {
    type: ToastType;
    position: ToastPosition;
    overlay: boolean;
    show: boolean;
    duration: number;
    closeOnClickOverlay: boolean;
    transition: string;
    forbidClick: boolean;
    closeOnClick: boolean;
}>;
export default _default;
