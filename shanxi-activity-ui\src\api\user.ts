import { http } from './request'
import type { UserInfo } from '@/types'

// 用户相关API
export const userApi = {
  // 缓存用户昵称
  cacheUserNickname(gameUserId: string, nickname: string): Promise<void> {
    return http.post('/users/cache-nickname', { gameUserId, nickname })
  },

  // 获取用户昵称
  getUserNickname(gameUserId: string): Promise<string> {
    return http.get(`/users/${gameUserId}/nickname`)
  },

  // 批量获取用户昵称
  getBatchUserNicknames(gameUserIds: string[]): Promise<Record<string, string>> {
    return http.post('/users/nicknames', { gameUserIds })
  },

  // 获取当前用户信息（这个接口需要游戏系统提供）
  getCurrentUserInfo(): Promise<UserInfo> {
    // 临时模拟数据，实际应该从游戏系统获取
    return Promise.resolve({
      gameUserId: 'test_user_001',
      nickname: '测试用户',
      role: 'alliance_leader',
      tongbaoBalance: 100000,
      avatar: ''
    })
  }
}
