export declare const Radio: import("../utils").WithInstall<import("vue").DefineComponent<{
    name: import("vue").PropType<unknown>;
    shape: {
        type: import("vue").PropType<import("../checkbox/Checker").CheckerShape>;
        default: import("../checkbox/Checker").CheckerShape;
    };
    disabled: BooleanConstructor;
    iconSize: (NumberConstructor | StringConstructor)[];
    modelValue: import("vue").PropType<unknown>;
    checkedColor: StringConstructor;
    labelPosition: import("vue").PropType<import("../checkbox/Checker").CheckerLabelPosition>;
    labelDisabled: BooleanConstructor;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "update:modelValue"[], "update:modelValue", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    name: import("vue").PropType<unknown>;
    shape: {
        type: import("vue").PropType<import("../checkbox/Checker").CheckerShape>;
        default: import("../checkbox/Checker").CheckerShape;
    };
    disabled: BooleanConstructor;
    iconSize: (NumberConstructor | StringConstructor)[];
    modelValue: import("vue").PropType<unknown>;
    checkedColor: StringConstructor;
    labelPosition: import("vue").PropType<import("../checkbox/Checker").CheckerLabelPosition>;
    labelDisabled: BooleanConstructor;
}>> & {
    "onUpdate:modelValue"?: ((...args: any[]) => any) | undefined;
}, {
    disabled: boolean;
    shape: import("../checkbox/Checker").CheckerShape;
    labelDisabled: boolean;
}>>;
export default Radio;
export type { RadioProps, RadioShape, RadioLabelPosition } from './Radio';
declare module 'vue' {
    interface GlobalComponents {
        VanRadio: typeof Radio;
    }
}
