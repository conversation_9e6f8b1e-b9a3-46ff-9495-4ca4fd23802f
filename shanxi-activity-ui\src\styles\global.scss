@use 'sass:color';
@use './variables.scss' as *;

// 全局重置样式
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: $font-family;
  font-size: $font-size-base;
  line-height: $line-height-base;
  color: $text-color-primary;
  background-color: $background-color-base;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100vh;
  overflow: hidden;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: $background-color-light;
}

::-webkit-scrollbar-thumb {
  background: $border-color-base;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: $text-color-disabled;
}

// 通用工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: $primary-color;
}

.text-success {
  color: $success-color;
}

.text-warning {
  color: $warning-color;
}

.text-error {
  color: $error-color;
}

.text-secondary {
  color: $text-color-secondary;
}

.text-disabled {
  color: $text-color-disabled;
}

// 间距工具类
.m-0 {
  margin: 0;
}

.mt-0 {
  margin-top: 0;
}

.mr-0 {
  margin-right: 0;
}

.mb-0 {
  margin-bottom: 0;
}

.ml-0 {
  margin-left: 0;
}

.m-xs {
  margin: $spacing-xs;
}

.mt-xs {
  margin-top: $spacing-xs;
}

.mr-xs {
  margin-right: $spacing-xs;
}

.mb-xs {
  margin-bottom: $spacing-xs;
}

.ml-xs {
  margin-left: $spacing-xs;
}

.m-sm {
  margin: $spacing-sm;
}

.mt-sm {
  margin-top: $spacing-sm;
}

.mr-sm {
  margin-right: $spacing-sm;
}

.mb-sm {
  margin-bottom: $spacing-sm;
}

.ml-sm {
  margin-left: $spacing-sm;
}

.m-md {
  margin: $spacing-md;
}

.mt-md {
  margin-top: $spacing-md;
}

.mr-md {
  margin-right: $spacing-md;
}

.mb-md {
  margin-bottom: $spacing-md;
}

.ml-md {
  margin-left: $spacing-md;
}

.m-lg {
  margin: $spacing-lg;
}

.mt-lg {
  margin-top: $spacing-lg;
}

.mr-lg {
  margin-right: $spacing-lg;
}

.mb-lg {
  margin-bottom: $spacing-lg;
}

.ml-lg {
  margin-left: $spacing-lg;
}

.p-0 {
  padding: 0;
}

.pt-0 {
  padding-top: 0;
}

.pr-0 {
  padding-right: 0;
}

.pb-0 {
  padding-bottom: 0;
}

.pl-0 {
  padding-left: 0;
}

.p-xs {
  padding: $spacing-xs;
}

.pt-xs {
  padding-top: $spacing-xs;
}

.pr-xs {
  padding-right: $spacing-xs;
}

.pb-xs {
  padding-bottom: $spacing-xs;
}

.pl-xs {
  padding-left: $spacing-xs;
}

.p-sm {
  padding: $spacing-sm;
}

.pt-sm {
  padding-top: $spacing-sm;
}

.pr-sm {
  padding-right: $spacing-sm;
}

.pb-sm {
  padding-bottom: $spacing-sm;
}

.pl-sm {
  padding-left: $spacing-sm;
}

.p-md {
  padding: $spacing-md;
}

.pt-md {
  padding-top: $spacing-md;
}

.pr-md {
  padding-right: $spacing-md;
}

.pb-md {
  padding-bottom: $spacing-md;
}

.pl-md {
  padding-left: $spacing-md;
}

.p-lg {
  padding: $spacing-lg;
}

.pt-lg {
  padding-top: $spacing-lg;
}

.pr-lg {
  padding-right: $spacing-lg;
}

.pb-lg {
  padding-bottom: $spacing-lg;
}

.pl-lg {
  padding-left: $spacing-lg;
}

// 布局工具类
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.justify-start {
  justify-content: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.align-start {
  align-items: flex-start;
}

.align-center {
  align-items: center;
}

.align-end {
  align-items: flex-end;
}

.flex-1 {
  flex: 1;
}

// 卡片样式
.card {
  background: $background-color-white;
  border-radius: $border-radius-base;
  box-shadow: $box-shadow-card;
  padding: $spacing-lg;
  margin-bottom: $spacing-md;
}

// 按钮样式增强
.btn-gradient {
  background: linear-gradient(135deg, $primary-color 0%, #{color.scale($primary-color, $lightness: 22.0779220779%)} 100%);
  border: none;
  color: white;

  &:hover {
    background: linear-gradient(135deg, #{color.scale($primary-color, $lightness: -9.1397849462%)} 0%, $primary-color 100%);
  }
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform 0.3s ease;
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(100%);
}

// 响应式工具类
@media (max-width: $screen-sm) {
  .hidden-xs {
    display: none !important;
  }
}

@media (min-width: $screen-sm) and (max-width: $screen-md) {
  .hidden-sm {
    display: none !important;
  }
}

@media (min-width: $screen-md) {
  .hidden-md-up {
    display: none !important;
  }
}
