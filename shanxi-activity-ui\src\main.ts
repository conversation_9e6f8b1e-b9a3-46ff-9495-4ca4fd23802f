import { createApp } from 'vue'
import { createPinia } from 'pinia'

// Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// Vant
import 'vant/lib/index.css'

// 自定义样式
import './styles/index.scss'

import App from './App.vue'
import router from './router'
import { setupPermissionDirectives } from './directives/permission'

const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

// 注册权限指令
setupPermissionDirectives(app)

app.mount('#app')
