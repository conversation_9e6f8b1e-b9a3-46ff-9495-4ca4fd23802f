<template>
  <div class="activity-participation">
    <!-- 活动信息 -->
    <div class="activity-info">
      <div class="activity-header">
        <div class="activity-title">
          <span class="activity-name"
            >恭喜玩家xxx 在活动中开启盲盒获得一等奖—100通宝奖励</span
          >
        </div>
        <div class="activity-meta">
          <span class="activity-time">活动时间：2025-7-25——2025-8-1</span>
        </div>
      </div>

      <div class="pool-info">
        <div class="pool-display">
          <span class="pool-label">奖池</span>
          <span class="pool-amount">99999</span>
          <span class="pool-note">(本活动由：xxxx发起)</span>
        </div>
        <el-button class="record-btn" size="small">记录</el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧：盲盒区域 -->
      <div class="lottery-section">
        <div class="gift-box">
          <div class="gift-placeholder">🎁</div>
          <el-button
            class="draw-btn"
            type="primary"
            size="large"
            @click="openBox"
            :disabled="remainingChances === 0"
          >
            打开盲盒 (剩余次数: {{ remainingChances }})
          </el-button>
        </div>
      </div>

      <!-- 右侧：任务区域 -->
      <div class="task-section">
        <div class="task-header">
          <span class="task-title">次数任务:</span>
          <span class="refresh-time">任务刷新时间: {{ refreshTime }}</span>
        </div>

        <div class="task-list">
          <div
            v-for="task in tasks"
            :key="task.id"
            class="task-item"
            :class="{ completed: task.completed }"
          >
            <div class="task-info">
              <span class="task-name"
                >{{ task.name }} ({{ task.progress }}/{{ task.target }})</span
              >
              <span class="task-status">{{
                task.completed ? "已完成" : "进行中"
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>


<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";

// 数据
const remainingChances = ref(0);
const refreshTime = ref("20:25");

const tasks = ref([
  {
    id: 1,
    name: "参与一局游戏",
    progress: 1,
    target: 1,
    completed: true,
  },
  {
    id: 2,
    name: "参与三局游戏",
    progress: 2,
    target: 3,
    completed: false,
  },
]);

// 开盲盒
const openBox = () => {
  if (remainingChances.value <= 0) {
    ElMessage.warning("没有剩余次数了");
    return;
  }

  // 模拟开盲盒
  ElMessage.success("恭喜获得奖励！");
  remainingChances.value--;
};
</script>

<style lang="scss" scoped>
.activity-participation {
  min-height: 100vh;
  background: #f0f2f5;
  padding: 20px;
}

.activity-info {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-header {
  margin-bottom: 16px;
}

.activity-name {
  font-size: 16px;
  color: #333;
  background: linear-gradient(90deg, #ffd700 0%, #ffa500 100%);
  padding: 8px 16px;
  border-radius: 20px;
  color: white;
  display: inline-block;
}

.activity-time {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
  display: block;
}

.pool-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pool-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pool-label {
  font-size: 14px;
  color: #666;
}

.pool-amount {
  font-size: 24px;
  font-weight: bold;
  color: #f56c6c;
}

.pool-note {
  font-size: 12px;
  color: #999;
}

.main-content {
  display: flex;
  gap: 20px;
}

.lottery-section {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.gift-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.gift-placeholder {
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 120px;
  background: linear-gradient(135deg, #ff6b6b, #feca57);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.draw-btn {
  padding: 12px 24px;
  font-size: 16px;
}

.task-section {
  width: 300px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.task-title {
  font-weight: bold;
  color: #333;
}

.refresh-time {
  font-size: 12px;
  color: #666;
}

.task-item {
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  margin-bottom: 8px;

  &.completed {
    background: #f6ffed;
    border-color: #b7eb8f;
  }
}

.task-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-name {
  font-size: 14px;
  color: #333;
}

.task-status {
  font-size: 12px;
  color: #52c41a;

  .task-item:not(.completed) & {
    color: #1890ff;
  }
}
</style>
