export declare const SwipeItem: import("../utils").WithInstall<import("vue").DefineComponent<{}, (() => JSX.Element) | undefined, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, import("vue").EmitsOptions, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>>;
export default SwipeItem;
declare module 'vue' {
    interface GlobalComponents {
        VanSwipeItem: typeof SwipeItem;
    }
}
