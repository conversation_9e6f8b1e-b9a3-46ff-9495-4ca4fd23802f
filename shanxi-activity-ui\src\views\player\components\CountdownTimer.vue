<template>
  <div class="countdown-timer" :class="timerClass">
    <div class="countdown-content">
      <div class="countdown-label" v-if="label">
        {{ label }}
      </div>
      
      <div class="countdown-display" v-if="!isExpired">
        <div class="time-unit" v-if="showDays && days > 0">
          <span class="time-value">{{ days }}</span>
          <span class="time-label">天</span>
        </div>
        
        <div class="time-unit" v-if="showHours">
          <span class="time-value">{{ formatTime(hours) }}</span>
          <span class="time-label">时</span>
        </div>
        
        <div class="time-unit" v-if="showMinutes">
          <span class="time-value">{{ formatTime(minutes) }}</span>
          <span class="time-label">分</span>
        </div>
        
        <div class="time-unit" v-if="showSeconds">
          <span class="time-value">{{ formatTime(seconds) }}</span>
          <span class="time-label">秒</span>
        </div>
      </div>
      
      <div class="countdown-expired" v-else>
        {{ expiredText }}
      </div>
      
      <div class="countdown-icon" v-if="showIcon">
        <el-icon>
          <component :is="iconComponent" />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Clock, AlarmClock, Timer } from '@element-plus/icons-vue'

interface Props {
  targetTime?: string | number // 目标时间戳或ISO字符串
  duration?: number // 倒计时秒数
  label?: string
  expiredText?: string
  showDays?: boolean
  showHours?: boolean
  showMinutes?: boolean
  showSeconds?: boolean
  showIcon?: boolean
  size?: 'small' | 'default' | 'large'
  type?: 'default' | 'warning' | 'danger'
  autoStart?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  expiredText: '已结束',
  showDays: true,
  showHours: true,
  showMinutes: true,
  showSeconds: true,
  showIcon: false,
  size: 'default',
  type: 'default',
  autoStart: true
})

const emit = defineEmits<{
  expired: []
  tick: [remainingSeconds: number]
}>()

const remainingSeconds = ref(0)
const timer = ref<NodeJS.Timeout | null>(null)

// 计算属性
const days = computed(() => Math.floor(remainingSeconds.value / (24 * 3600)))
const hours = computed(() => Math.floor((remainingSeconds.value % (24 * 3600)) / 3600))
const minutes = computed(() => Math.floor((remainingSeconds.value % 3600) / 60))
const seconds = computed(() => remainingSeconds.value % 60)

const isExpired = computed(() => remainingSeconds.value <= 0)

const timerClass = computed(() => [
  `countdown-${props.size}`,
  `countdown-${props.type}`,
  {
    'is-expired': isExpired.value,
    'is-warning': remainingSeconds.value <= 300 && remainingSeconds.value > 60, // 5分钟内警告
    'is-danger': remainingSeconds.value <= 60 && remainingSeconds.value > 0 // 1分钟内危险
  }
])

const iconComponent = computed(() => {
  if (isExpired.value) return Clock
  if (remainingSeconds.value <= 60) return AlarmClock
  return Timer
})

// 方法
const formatTime = (time: number) => {
  return time.toString().padStart(2, '0')
}

const calculateRemainingTime = () => {
  if (props.duration !== undefined) {
    // 使用持续时间
    return props.duration
  } else if (props.targetTime) {
    // 使用目标时间
    const target = typeof props.targetTime === 'string' 
      ? new Date(props.targetTime).getTime()
      : props.targetTime
    const now = Date.now()
    return Math.max(0, Math.floor((target - now) / 1000))
  }
  return 0
}

const startTimer = () => {
  if (timer.value) return
  
  remainingSeconds.value = calculateRemainingTime()
  
  timer.value = setInterval(() => {
    if (props.duration === undefined && props.targetTime) {
      // 重新计算剩余时间（用于目标时间模式）
      remainingSeconds.value = calculateRemainingTime()
    } else {
      // 递减模式（用于持续时间模式）
      remainingSeconds.value = Math.max(0, remainingSeconds.value - 1)
    }
    
    emit('tick', remainingSeconds.value)
    
    if (remainingSeconds.value <= 0) {
      stopTimer()
      emit('expired')
    }
  }, 1000)
}

const stopTimer = () => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
}

const resetTimer = () => {
  stopTimer()
  remainingSeconds.value = calculateRemainingTime()
}

// 暴露方法
defineExpose({
  start: startTimer,
  stop: stopTimer,
  reset: resetTimer,
  remainingSeconds: computed(() => remainingSeconds.value)
})

// 生命周期
onMounted(() => {
  if (props.autoStart) {
    startTimer()
  } else {
    remainingSeconds.value = calculateRemainingTime()
  }
})

onUnmounted(() => {
  stopTimer()
})
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.countdown-timer {
  display: inline-flex;
  align-items: center;
  
  .countdown-content {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    
    .countdown-label {
      font-size: $font-size-sm;
      color: $text-color-secondary;
      margin-right: $spacing-xs;
    }
    
    .countdown-display {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
      
      .time-unit {
        display: flex;
        flex-direction: column;
        align-items: center;
        
        .time-value {
          font-weight: 600;
          color: $text-color-primary;
          line-height: 1;
        }
        
        .time-label {
          font-size: $font-size-sm;
          color: $text-color-secondary;
          line-height: 1;
          margin-top: 2px;
        }
      }
    }
    
    .countdown-expired {
      color: $text-color-disabled;
      font-weight: 500;
    }
    
    .countdown-icon {
      color: $text-color-secondary;
    }
  }
  
  // 尺寸变体
  &.countdown-small {
    .countdown-content {
      .countdown-display {
        .time-unit {
          .time-value {
            font-size: $font-size-sm;
          }
          
          .time-label {
            font-size: 10px;
          }
        }
      }
    }
  }
  
  &.countdown-large {
    .countdown-content {
      .countdown-display {
        gap: $spacing-sm;
        
        .time-unit {
          .time-value {
            font-size: $font-size-xl;
          }
          
          .time-label {
            font-size: $font-size-base;
          }
        }
      }
    }
  }
  
  // 状态变体
  &.countdown-warning,
  &.is-warning {
    .countdown-content {
      .countdown-display {
        .time-unit {
          .time-value {
            color: $warning-color;
          }
        }
      }
    }
  }
  
  &.countdown-danger,
  &.is-danger {
    .countdown-content {
      .countdown-display {
        .time-unit {
          .time-value {
            color: $error-color;
            animation: pulse 1s ease-in-out infinite;
          }
        }
      }
    }
  }
  
  &.is-expired {
    .countdown-content {
      .countdown-expired {
        color: $text-color-disabled;
      }
      
      .countdown-icon {
        color: $text-color-disabled;
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}
</style>
